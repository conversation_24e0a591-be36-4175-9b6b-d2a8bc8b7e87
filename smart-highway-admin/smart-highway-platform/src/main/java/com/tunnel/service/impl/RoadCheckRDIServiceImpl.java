package com.tunnel.service.impl;

import com.tunnel.common.core.domain.entity.SysUser;
import com.tunnel.common.utils.*;
import com.tunnel.common.utils.poi.ExcelUtil;
import com.tunnel.common.utils.WordDocumentUtils;
import com.tunnel.domain.BatchAddResponse;
import com.tunnel.domain.Road;
import com.tunnel.domain.RoadCheckRDI;
import com.tunnel.mapper.RoadCheckRDMapper;
import com.tunnel.mapper.RoadMapper;
import com.tunnel.service.CheckTeamService;
import com.tunnel.service.RoadCheckRDIService;
import com.tunnel.service.RoadCheckSRIService;
import lombok.extern.slf4j.Slf4j;
import org.apache.poi.ss.usermodel.*;
import org.apache.poi.ss.util.CellRangeAddress;
import org.apache.poi.xssf.streaming.SXSSFWorkbook;
import org.apache.poi.xwpf.usermodel.ParagraphAlignment;
import org.apache.poi.xwpf.usermodel.XWPFDocument;
import org.apache.poi.xwpf.usermodel.XWPFFooter;
import org.apache.poi.xwpf.usermodel.XWPFHeader;
import org.apache.poi.xwpf.usermodel.XWPFParagraph;
import org.apache.poi.xwpf.usermodel.XWPFRun;
import org.apache.poi.xwpf.usermodel.XWPFTable;
import org.apache.poi.xwpf.usermodel.XWPFTableCell;
import org.apache.poi.xwpf.usermodel.XWPFTableRow;
import org.openxmlformats.schemas.wordprocessingml.x2006.main.CTBorder;
import org.openxmlformats.schemas.wordprocessingml.x2006.main.CTTcBorders;
import org.openxmlformats.schemas.wordprocessingml.x2006.main.CTTcPr;
import org.openxmlformats.schemas.wordprocessingml.x2006.main.STBorder;
import org.openxmlformats.schemas.wordprocessingml.x2006.main.STMerge;
import org.openxmlformats.schemas.wordprocessingml.x2006.main.STVerticalJc;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.io.ClassPathResource;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.io.BufferedInputStream;
import java.io.FileInputStream;
import java.io.IOException;
import java.io.InputStream;
import java.math.BigDecimal;
import java.math.BigInteger;
import java.net.URLEncoder;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.HashSet;

/**
 * 路面车辙深度检测信息Service业务层处理
 *
 * <AUTHOR>
 * @date 2024-12-02
 */
@Service
@Slf4j
public class RoadCheckRDIServiceImpl implements RoadCheckRDIService {
    @Autowired
    private RoadCheckRDMapper roadCheckRDMapper;

    @Autowired
    private RoadMapper roadMapper;

    @Autowired
    private CheckTeamService checkTeamService;

    @Resource
    private RoadCheckSRIService roadCheckSRIService;

    // 每页导出数量
    private static final int PAGE_SIZE = 5000;

    // 导出线程池大小
    private static final int EXPORT_THREADS = 5;

    /**
     * 查询路面车辙深度检测信息
     *
     * @param id 路面车辙深度检测信息主键
     * @return 路面车辙深度检测信息
     */
    @Override
    public RoadCheckRDI selectScRoadCheckRdById(Long id) {
        return roadCheckRDMapper.selectRoadCheckRDById(id);
    }

    /**
     * 查询路面车辙深度检测信息列表
     *
     * @param RoadCheckRDI 路面车辙深度检测信息
     * @return 路面车辙深度检测信息
     */
    @Override
    public List<RoadCheckRDI> selectScRoadCheckRdList(RoadCheckRDI RoadCheckRDI) {
        return roadCheckRDMapper.selectRoadCheckRDList(RoadCheckRDI);
    }

    /**
     * 新增路面车辙深度检测信息
     *
     * @param RoadCheckRDI 路面车辙深度检测信息
     * @return 结果
     */
    @Override
    public int insertScRoadCheckRd(RoadCheckRDI RoadCheckRDI) {
        SysUser user = SecurityUtils.getLoginUser().getUser();
        RoadCheckRDI.setCreator(user.getUserId());
        RoadCheckRDI.setModifier(user.getUserId());

        // 计算百米段和公里段
        if (RoadCheckRDI.getStartCode() != null && !RoadCheckRDI.getStartCode().isEmpty()) {
            RoadCheckRDI.setHundredSection(StakeCodeUtil.calculateHundredSection(RoadCheckRDI.getStartCode()));
            RoadCheckRDI.setThousandSection(StakeCodeUtil.calculateThousandSection(RoadCheckRDI.getStartCode()));
        }

        return roadCheckRDMapper.insertRoadCheckRD(RoadCheckRDI);
    }

    /**
     * 修改路面车辙深度检测信息
     *
     * @param RoadCheckRDI 路面车辙深度检测信息
     * @return 结果
     */
    @Override
    public int updateScRoadCheckRd(RoadCheckRDI RoadCheckRDI) {
        SysUser user = SecurityUtils.getLoginUser().getUser();
        RoadCheckRDI.setModifier(user.getUserId());

        // 计算百米段和公里段
        if (RoadCheckRDI.getStartCode() != null && !RoadCheckRDI.getStartCode().isEmpty()) {
            RoadCheckRDI.setHundredSection(StakeCodeUtil.calculateHundredSection(RoadCheckRDI.getStartCode()));
            RoadCheckRDI.setThousandSection(StakeCodeUtil.calculateThousandSection(RoadCheckRDI.getStartCode()));
        }

        return roadCheckRDMapper.updateRoadCheckRD(RoadCheckRDI);
    }

    /**
     * 批量删除路面车辙深度检测信息
     *
     * @param ids 需要删除的路面车辙深度检测信息主键
     * @return 结果
     */
    @Override
    public int deleteScRoadCheckRdByIds(Long[] ids) {
        return roadCheckRDMapper.deleteRoadCheckRDByIds(ids);
    }

    /**
     * 删除路面车辙深度检测信息信息
     *
     * @param id 路面车辙深度检测信息主键
     * @return 结果
     */
    @Override
    public int deleteScRoadCheckRdById(Long id) {
        return roadCheckRDMapper.deleteRoadCheckRDById(id);
    }

    /**
     * 根据道路ID获取路面车辙深度检测信息
     *
     * @param roadId 道路ID
     * @return 路面车辙深度检测信息集合
     */
    @Override
    public List<RoadCheckRDI> selectScRoadCheckRdByRoadId(Long roadId) {
        return roadCheckRDMapper.selectRoadCheckRDByRoadId(roadId);
    }

    /**
     * 批量导入路面车辙深度检测数据
     * 支持单工作表格式：Sheet1中同时包含上行和下行数据
     *
     * @param file 导入文件
     * @param roadId 道路ID
     * @return 导入结果
     */
    @Override
    @Transactional
    public BatchAddResponse batchImport(MultipartFile file, Long roadId) {
        BatchAddResponse addResponse = new BatchAddResponse();
        InputStream fileStream = null;
        try {
            // 导入前先删除该道路的所有RDI数据
            int deletedCount = deleteScRoadCheckRdByRoadId(roadId);
            log.info("导入前删除道路ID {} 的RDI数据：{} 条", roadId, deletedCount);

            // 生成本地缓存路径
            String localFile = FileUtil.saveExcelFile(file);
            fileStream = new FileInputStream(localFile);
            BufferedInputStream bufferedInputStream = new BufferedInputStream(fileStream);

            // 读取Excel文件
            Workbook workbook;
            if (file.getOriginalFilename().endsWith(".xlsx")) {
                workbook = WorkbookFactory.create(bufferedInputStream);
            } else {
                addResponse.setStatus(1);
                addResponse.setMsg("请使用Excel 2007及以上版本的文件（.xlsx格式）！");
                addResponse.generateSummary();
                return addResponse;
            }

            List<RoadCheckRDI> resultList = new ArrayList<>();

            // 处理Sheet1（包含上行和下行数据）
            Sheet sheet1 = workbook.getSheetAt(0);
            if (sheet1 != null) {
                List<RoadCheckRDI> allDirectionData = processSheetWithValidation(sheet1, roadId, "Sheet1", addResponse);
                resultList.addAll(allDirectionData);
                log.info("Sheet1处理数据：{} 条", allDirectionData.size());
            }

            // 根据roadType分组并处理段位信息
            if (!resultList.isEmpty()) {
                log.info("开始处理RDI数据的段位信息，总计 {} 条记录", resultList.size());
                StakeCodeUtil.processSectionsByRoadType(resultList);
            }

            // 只插入校验通过的数据
            if (!resultList.isEmpty()) {
                List<List<RoadCheckRDI>> splitList = CollectUtil.splitList(resultList, 1000);
                for (List<RoadCheckRDI> tempList : splitList) {
                    roadCheckRDMapper.batchInsert(tempList);
                }
                addResponse.addSuccessCount(resultList.size());
                log.info("成功导入RDI数据：{} 条", resultList.size());

                // 输出最后一行数据的段位信息用于验证
                if (!resultList.isEmpty()) {
                    RoadCheckRDI lastRecord = resultList.get(resultList.size() - 1);
                    log.info("导入RDI数据最后一条记录段位信息：起始桩号={}, 结束桩号={}, 百米段={}, 公里段={}",
                            lastRecord.getStartCode(), lastRecord.getEndCode(),
                            lastRecord.getHundredSection(), lastRecord.getThousandSection());
                }
            }

            // 设置最终状态
            if (addResponse.getSuccessCount() > 0 && addResponse.getFailCount() == 0) {
                addResponse.setStatus(0);
                addResponse.setMsg("导入成功");
            } else if (addResponse.getSuccessCount() > 0 && addResponse.getFailCount() > 0) {
                addResponse.setStatus(0);
                addResponse.setMsg("部分导入成功");
            } else {
                addResponse.setStatus(1);
                addResponse.setMsg("导入失败");
            }

            addResponse.generateSummary();
            workbook.close();

        } catch (IOException e) {
            log.error("文件解析失败", e);
            addResponse.setStatus(1);
            addResponse.setMsg("文件解析失败: " + e.getMessage());
            addResponse.generateSummary();
        } catch (Exception e) {
            log.error("导入数据失败", e);
            addResponse.setStatus(1);
            addResponse.setMsg("导入数据失败: " + e.getMessage());
            addResponse.generateSummary();
        } finally {
            try {
                if (fileStream != null) {
                    fileStream.close();
                }
            } catch (IOException e) {
                log.error("excel文件读取失败, 失败原因：{}", e);
            }
        }
        return addResponse;
    }

    /**
     * 处理工作表的RDI数据（包含详细校验）
     *
     * @param sheet 工作表
     * @param roadId 道路ID
     * @param sheetName 工作表名称
     * @param addResponse 响应对象
     * @return 解析后的数据列表
     */
    private List<RoadCheckRDI> processSheetWithValidation(Sheet sheet, Long roadId, String sheetName, BatchAddResponse addResponse) {
        List<RoadCheckRDI> resultList = new ArrayList<>();

        // 从第6行开始读取数据（索引从0开始，第6行对应索引5），跳过表头和标题行
        int startRow = 5;
        int rowCount = sheet.getPhysicalNumberOfRows();

        if (rowCount <= startRow) {
            log.warn("工作表 {} 中没有找到有效数据行", sheetName);
            return resultList;
        }

        // 预处理：收集所有有效的数据行
        List<Integer> validRowIndices = new ArrayList<>();
        for (int i = startRow; i < rowCount; i++) {
            Row row = sheet.getRow(i);
            if (row == null) {
                continue;
            }

            // 检查第一列是否为空（起始桩号）
            Cell stakeCell = row.getCell(0);
            if (stakeCell == null || getCellStringValue(stakeCell).trim().isEmpty()) {
                // 检查是否为空行
                boolean isEmptyRow = true;
                for (int j = 0; j < 10; j++) { // 检查10列数据（包括路面类型）
                    Cell cell = row.getCell(j);
                    if (cell != null && !getCellStringValue(cell).trim().isEmpty()) {
                        isEmptyRow = false;
                        break;
                    }
                }
                if (!isEmptyRow) {
                    validRowIndices.add(i); // 非空行但起始桩号为空，仍算作有效行（会报错）
                }
            } else {
                validRowIndices.add(i); // 有起始桩号的行
            }
        }

        // 遍历数据行
        for (int idx = 0; idx < validRowIndices.size(); idx++) {
            int i = validRowIndices.get(idx);
            Row row = sheet.getRow(i);
            if (row == null) {
                continue;
            }

            int excelRowNum = i + 1; // Excel行号从1开始
            boolean rowHasError = false;

            // 检查第一列是否为空（起始桩号）
            Cell startStakeCell = row.getCell(0);
            if (startStakeCell == null || getCellStringValue(startStakeCell).trim().isEmpty()) {
                // 跳过前6行的错误报告（表头行）
                if (excelRowNum > 6) {
                    addResponse.addError(sheetName, excelRowNum, "起始桩号", "起始桩号不能为空");
                }
                rowHasError = true;
                continue;
            }

            // 检查第三列是否为空（结束桩号）
            Cell endStakeCell = row.getCell(2);
            if (endStakeCell == null || getCellStringValue(endStakeCell).trim().isEmpty()) {
                // 跳过前6行的错误报告（表头行）
                if (excelRowNum > 6) {
                    addResponse.addError(sheetName, excelRowNum, "结束桩号", "结束桩号不能为空");
                }
                rowHasError = true;
                continue;
            }

            try {
                // 获取桩号信息
                String startCode = getCellStringValue(row.getCell(0)).trim();
                String endCode = getCellStringValue(row.getCell(2)).trim();

                // 格式化桩号
                String formattedStartCode = StakeCodeUtil.formatStakeCode(startCode);
                String formattedEndCode = StakeCodeUtil.formatStakeCode(endCode);

                // 获取上行数据 (DEF列)
                BigDecimal upLeftRd = validateAndGetDecimalValue(row.getCell(3), sheetName, excelRowNum, "上行左轮迹", addResponse);
                BigDecimal upRightRd = validateAndGetDecimalValue(row.getCell(4), sheetName, excelRowNum, "上行右轮迹", addResponse);
                BigDecimal upMaxRd = validateAndGetDecimalValue(row.getCell(5), sheetName, excelRowNum, "上行最大车辙", addResponse);

                // 获取下行数据 (GHI列)
                BigDecimal downLeftRd = validateAndGetDecimalValue(row.getCell(6), sheetName, excelRowNum, "下行左轮迹", addResponse);
                BigDecimal downRightRd = validateAndGetDecimalValue(row.getCell(7), sheetName, excelRowNum, "下行右轮迹", addResponse);
                BigDecimal downMaxRd = validateAndGetDecimalValue(row.getCell(8), sheetName, excelRowNum, "下行最大车辙", addResponse);

                // 校验和设置路面类型（第10列，索引9）
                String roadType = getCellStringValue(row.getCell(9));
                if (roadType == null || roadType.trim().isEmpty()) {
                    roadType = "沥青路面"; // 默认值
                } else {
                    roadType = roadType.trim();
                }

                // 获取当前用户
                SysUser user = SecurityUtils.getLoginUser().getUser();

                // 创建上行记录
                if (upLeftRd != null || upRightRd != null || upMaxRd != null) {
                    RoadCheckRDI upRecord = new RoadCheckRDI();
                    upRecord.setRoadId(roadId);
                    upRecord.setStartCode(formattedStartCode);
                    upRecord.setEndCode(formattedEndCode);
                    upRecord.setDirection(1); // 上行
                    upRecord.setLeftRd(upLeftRd);
                    upRecord.setRightRd(upRightRd);
                    upRecord.setMaxRd(upMaxRd);
                    upRecord.setRoadType(roadType);
                    upRecord.setCreator(user.getUserId());
                    upRecord.setModifier(user.getUserId());

                    resultList.add(upRecord);
                }

                // 创建下行记录
                if (downLeftRd != null || downRightRd != null || downMaxRd != null) {
                    RoadCheckRDI downRecord = new RoadCheckRDI();
                    downRecord.setRoadId(roadId);
                    downRecord.setStartCode(formattedStartCode);
                    downRecord.setEndCode(formattedEndCode);
                    downRecord.setDirection(2); // 下行
                    downRecord.setLeftRd(downLeftRd);
                    downRecord.setRightRd(downRightRd);
                    downRecord.setMaxRd(downMaxRd);
                    downRecord.setRoadType(roadType);
                    downRecord.setCreator(user.getUserId());
                    downRecord.setModifier(user.getUserId());

                    resultList.add(downRecord);
                }

            } catch (Exception e) {
                log.error("处理第{}行数据时发生错误", excelRowNum, e);
                addResponse.addError(sheetName, excelRowNum, "数据处理", "处理数据时发生错误：" + e.getMessage());
            }
        }

        log.info("工作表 {} 处理完成，有效数据：{} 条", sheetName, resultList.size());
        return resultList;
    }

    /**
     * 校验并获取BigDecimal值
     */
    private BigDecimal validateAndGetDecimalValue(Cell cell, String sheetName, int rowNum,
                                                  String fieldName, BatchAddResponse addResponse) {
        if (cell == null) {
            return null;
        }

        try {
            switch (cell.getCellType()) {
                case NUMERIC:
                    if (DateUtil.isCellDateFormatted(cell)) {
                        // 跳过前6行的错误报告（表头行）
                        if (rowNum > 6) {
                            addResponse.addError(sheetName, rowNum, fieldName, "期望数值类型，但发现日期格式");
                        }
                        return null;
                    }
                    double value = cell.getNumericCellValue();
                    return new BigDecimal(String.valueOf(value));
                case STRING:
                    String strValue = cell.getStringCellValue().trim();
                    if (strValue.isEmpty() || "-".equals(strValue)) {
                        return null;
                    }
                    try {
                        return new BigDecimal(strValue);
                    } catch (NumberFormatException e) {
                        // 跳过前6行的错误报告（表头行）
                        if (rowNum > 6) {
                            addResponse.addError(sheetName, rowNum, fieldName, "无法转换为数值：" + strValue);
                        }
                        return null;
                    }
                case FORMULA:
                    try {
                        double formulaValue = cell.getNumericCellValue();
                        if (formulaValue < 0) {
                            // 跳过前6行的错误报告（表头行）
                            if (rowNum > 6) {
                                addResponse.addError(sheetName, rowNum, fieldName, "车辙深度不能为负数：" + formulaValue);
                            }
                            return null;
                        }
                        return new BigDecimal(String.valueOf(formulaValue));
                    } catch (Exception e) {
                        // 跳过前6行的错误报告（表头行）
                        if (rowNum > 6) {
                            addResponse.addError(sheetName, rowNum, fieldName, "公式计算错误");
                        }
                        return null;
                    }
                default:
                    return null;
            }
        } catch (Exception e) {
            // 跳过前6行的错误报告（表头行）
            if (rowNum > 6) {
                addResponse.addError(sheetName, rowNum, fieldName, "获取数值失败：" + e.getMessage());
            }
            return null;
        }
    }

    /**
     * 获取单元格的字符串值
     */
    private String getCellStringValue(Cell cell) {
        if (cell == null) {
            return "";
        }

        try {
            switch (cell.getCellType()) {
                case STRING:
                    return cell.getStringCellValue().trim();
                case NUMERIC:
                    if (DateUtil.isCellDateFormatted(cell)) {
                        return new SimpleDateFormat("yyyy-MM-dd").format(cell.getDateCellValue());
                    }
                    // 对于数字，转为字符串并处理格式
                    double numValue = cell.getNumericCellValue();
                    if (Math.floor(numValue) == numValue) {
                        // 整数值，去除小数点和小数部分
                        return String.valueOf((long)numValue);
                    } else {
                        // 保留原始精度
                        return String.valueOf(numValue);
                    }
                case BOOLEAN:
                    return String.valueOf(cell.getBooleanCellValue());
                case FORMULA:
                    try {
                        double formulaValue = cell.getNumericCellValue();
                        if (Math.floor(formulaValue) == formulaValue) {
                            // 整数值，去除小数点和小数部分
                            return String.valueOf((long)formulaValue);
                        } else {
                            // 保留原始精度
                            return String.valueOf(formulaValue);
                        }
                    } catch (Exception e) {
                        return cell.getStringCellValue().trim();
                    }
                default:
                    return "";
            }
        } catch (Exception e) {
            log.warn("获取单元格字符串值失败: {}", e.getMessage());
            return "";
        }
    }

    /**
     * 获取单元格的BigDecimal值
     */
    private BigDecimal getCellDecimalValue(Cell cell) {
        if (cell == null) {
            return null;
        }

        try {
            switch (cell.getCellType()) {
                case NUMERIC:
                    if (DateUtil.isCellDateFormatted(cell)) {
                        return null;
                    }
                    // 对于数字，直接转为BigDecimal
                    return new BigDecimal(String.valueOf(cell.getNumericCellValue()));
                case STRING:
                    String strValue = cell.getStringCellValue().trim();
                    if (strValue.isEmpty()) {
                        return null;
                    }
                    try {
                        return new BigDecimal(strValue);
                    } catch (NumberFormatException e) {
                        return null;
                    }
                default:
                    return null;
            }
        } catch (Exception e) {
            log.warn("获取单元格数值值失败: {}", e.getMessage());
            return null;
        }
    }

    /**
     * 优化导出路面车辙深度检测信息
     *
     * @param response HTTP响应
     * @param RoadCheckRDI 查询条件
     */
    @Override
    public void exportOptimized(HttpServletResponse response, RoadCheckRDI RoadCheckRDI) {
        List<RoadCheckRDI> list = roadCheckRDMapper.selectRoadCheckRDList(RoadCheckRDI);
        ExcelUtil<RoadCheckRDI> util = new ExcelUtil<>(RoadCheckRDI.class);
        util.exportExcel(response, list, "路面车辙深度检测信息数据");
    }

    /**
     * 获取路面车辙深度检测信息记录数
     *
     * @param RoadCheckRDI 查询条件
     * @return 记录数
     */
    @Override
    public int countScRoadCheckRd(RoadCheckRDI RoadCheckRDI) {
        return roadCheckRDMapper.countRoadCheckRD(RoadCheckRDI);
    }

    @Override
    public void exportRDIByDirection(HttpServletResponse response, Long roadId) {
        try {
            // 查询指定路线的RDI数据
            RoadCheckRDI query = new RoadCheckRDI();
            query.setRoadId(roadId);
            query.setDirection(1);
            List<RoadCheckRDI> upData = roadCheckRDMapper.selectRoadCheckRDList(query);
            query.setDirection(2);
            List<RoadCheckRDI> downData = roadCheckRDMapper.selectRoadCheckRDList(query);

            // 检查是否有数据可以导出
            if (CollectionUtils.isEmpty(upData) && CollectionUtils.isEmpty(downData)) {
                response.setContentType("application/json;charset=UTF-8");
                response.getWriter().write("{\"code\":500,\"msg\":\"没有找到对应的RDI数据\"}");
                return;
            }

            // 合并上行下行数据用于获取桩号范围
            List<RoadCheckRDI> allData = new ArrayList<>();
            allData.addAll(upData);
            allData.addAll(downData);

            // 查询道路基本信息
            Road road = roadMapper.selectRoadById(roadId);
            if (road == null) {
                response.setContentType("application/json;charset=UTF-8");
                response.getWriter().write("{\"code\":500,\"msg\":\"路线信息不存在\"}");
                return;
            }

            String roadName = road.getRoadName();
            String companyName = road.getCompanyName() != null ? road.getCompanyName() : "湖北交投智能检测股份有限公司";
            String startCode = road.getStartCode();
            String endCode = road.getEndCode();

            // 加载RDI模板
            ClassPathResource resource = new ClassPathResource("static/rdi-template.xlsx");
            InputStream templateInputStream = resource.getInputStream();
            Workbook templateWorkbook = WorkbookFactory.create(templateInputStream);

            // 创建新的工作簿用于导出
            SXSSFWorkbook workbook = new SXSSFWorkbook();

            try {
                // 获取模板的第一个Sheet
                Sheet templateSheet = templateWorkbook.getSheetAt(0);

                // 创建十米数据Sheet
                Sheet tenMeterSheet = workbook.createSheet("十米");
                copyRDISheetStructure(templateSheet, tenMeterSheet, workbook);
                fillRDIDataToTemplate(tenMeterSheet, upData, downData, roadName, companyName, startCode, endCode);

                // 创建百米汇总Sheet
                Sheet hundredMeterSheet = workbook.createSheet("百米汇总");
                copyRDIHundredMeterSheetStructure(templateSheet, hundredMeterSheet, workbook);

                // 计算百米维度的汇总数据
                List<Map<String, Object>> hundredSectionData = calculateRDIHundredSectionSummary(allData);
                fillRDIHundredSectionDataToTemplate(hundredMeterSheet, hundredSectionData, roadName, companyName, startCode, endCode);

                // 创建公里汇总Sheet
                Sheet kilometerSheet = workbook.createSheet("公里汇总");
                copyRDIKilometerSheetStructure(templateSheet, kilometerSheet, workbook);

                // 计算公里维度的汇总数据
                List<Map<String, Object>> kilometerSectionData = calculateRDIKilometerSectionSummary(allData);
                fillRDIKilometerSectionDataToTemplate(kilometerSheet, kilometerSectionData, roadName);

                // 设置响应头
                String fileName = URLEncoder.encode(
                        "沥青路面车辙深度（RD）检测记录_" + road.getRoadCode() + "_" +
                                DateUtils.dateTimeNow() + ".xlsx", "UTF-8"
                );
                response.setContentType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
                response.setHeader("Content-Disposition", "attachment; filename=" + fileName);

                // 输出文件
                workbook.write(response.getOutputStream());

            } finally {
                templateWorkbook.close();
                workbook.close();
                templateInputStream.close();
            }

        } catch (Exception e) {
            log.error("导出RDI数据失败", e);
            try {
                response.setContentType("application/json;charset=UTF-8");
                response.getWriter().write("{\"code\":500,\"msg\":\"导出失败：" + e.getMessage() + "\"}");
            } catch (IOException ex) {
                log.error("写入错误响应失败", ex);
            }
        }
    }

    /**
     * 复制RDI百米汇总Sheet结构
     */
    private void copyRDIHundredMeterSheetStructure(Sheet templateSheet, Sheet newSheet, SXSSFWorkbook workbook) {
        // 复制列宽（7列：A-G）
        for (int i = 0; i < 7; i++) {
            if (i < templateSheet.getRow(0).getLastCellNum()) {
                newSheet.setColumnWidth(i, templateSheet.getColumnWidth(i));
            } else {
                newSheet.setColumnWidth(i, 15 * 256); // 默认列宽
            }
        }

        // 手动创建第一行标题，不复制模板样式
        Row titleRow = newSheet.createRow(0);
        titleRow.setHeight((short) 600); // 设置标题行高

        // 创建标题样式
        CellStyle titleStyle = workbook.createCellStyle();
        titleStyle.setAlignment(HorizontalAlignment.CENTER);
        titleStyle.setVerticalAlignment(VerticalAlignment.CENTER);
        titleStyle.setBorderTop(BorderStyle.THIN);
        titleStyle.setBorderRight(BorderStyle.THIN);
        titleStyle.setBorderBottom(BorderStyle.THIN);
        titleStyle.setBorderLeft(BorderStyle.THIN);

        // 创建标题字体
        Font titleFont = workbook.createFont();
        titleFont.setBold(true);
        titleFont.setFontHeightInPoints((short) 16);
        titleFont.setFontName("宋体");
        titleStyle.setFont(titleFont);

        // 创建第一行的第一个单元格并设置标题文本
        Cell titleCell = titleRow.createCell(0);
        titleCell.setCellValue("沥青路面车辙深度（RD）检测记录");
        titleCell.setCellStyle(titleStyle);

        // 合并第一行的所有列（A到G列）
        newSheet.addMergedRegion(new CellRangeAddress(0, 0, 0, 6));

        // 为其他被合并的单元格也创建相同的样式
        for (int i = 1; i <= 6; i++) {
            Cell cell = titleRow.createCell(i);
            cell.setCellStyle(titleStyle);
        }
    }

    /**
     * 复制RDI公里汇总Sheet结构
     */
    private void copyRDIKilometerSheetStructure(Sheet templateSheet, Sheet newSheet, SXSSFWorkbook workbook) {
        // 复制列宽（10列：A-J）
        for (int i = 0; i < 10; i++) {
            if (i < templateSheet.getRow(0).getLastCellNum()) {
                newSheet.setColumnWidth(i, templateSheet.getColumnWidth(i));
            } else {
                newSheet.setColumnWidth(i, 15 * 256); // 默认列宽
            }
        }

        // 手动创建前两行标题，合并A-J列和1-2行
        // 第一行
        Row titleRow1 = newSheet.createRow(0);
        titleRow1.setHeight((short) 600);

        // 第二行
        Row titleRow2 = newSheet.createRow(1);
        titleRow2.setHeight((short) 600);

        // 创建标题样式
        CellStyle titleStyle = workbook.createCellStyle();
        titleStyle.setAlignment(HorizontalAlignment.CENTER);
        titleStyle.setVerticalAlignment(VerticalAlignment.CENTER);
        titleStyle.setBorderTop(BorderStyle.THIN);
        titleStyle.setBorderRight(BorderStyle.THIN);
        titleStyle.setBorderBottom(BorderStyle.THIN);
        titleStyle.setBorderLeft(BorderStyle.THIN);

        // 创建标题字体
        Font titleFont = workbook.createFont();
        titleFont.setBold(true);
        titleFont.setFontHeightInPoints((short) 16);
        titleFont.setFontName("宋体");
        titleStyle.setFont(titleFont);

        // 创建第一行的第一个单元格，标题将在fillRDIKilometerSectionDataToTemplate中设置
        Cell titleCell = titleRow1.createCell(0);
        titleCell.setCellStyle(titleStyle);

        // 合并第1、2行的A到J列
        newSheet.addMergedRegion(new CellRangeAddress(0, 1, 0, 9));

        // 为其他被合并的单元格也创建相同的样式
        for (int row = 0; row <= 1; row++) {
            Row currentRow = (row == 0) ? titleRow1 : titleRow2;
            for (int col = (row == 0 ? 1 : 0); col <= 9; col++) {
                Cell cell = currentRow.createCell(col);
                cell.setCellStyle(titleStyle);
            }
        }
    }

    /**
     * 复制RDI Sheet结构
     */
    private void copyRDISheetStructure(Sheet templateSheet, Sheet newSheet, SXSSFWorkbook workbook) {
        // 复制列宽（9列：A-I）
        for (int i = 0; i < 9; i++) {
            if (i < templateSheet.getRow(0).getLastCellNum()) {
                newSheet.setColumnWidth(i, templateSheet.getColumnWidth(i));
            } else {
                newSheet.setColumnWidth(i, 15 * 256); // 默认列宽
            }
        }

        // 手动创建第一行标题，不复制模板样式
        Row titleRow = newSheet.createRow(0);
        titleRow.setHeight((short) 600); // 设置标题行高

        // 创建标题样式
        CellStyle titleStyle = workbook.createCellStyle();
        titleStyle.setAlignment(HorizontalAlignment.CENTER);
        titleStyle.setVerticalAlignment(VerticalAlignment.CENTER);
        titleStyle.setBorderTop(BorderStyle.THIN);
        titleStyle.setBorderRight(BorderStyle.THIN);
        titleStyle.setBorderBottom(BorderStyle.THIN);
        titleStyle.setBorderLeft(BorderStyle.THIN);

        // 创建标题字体
        Font titleFont = workbook.createFont();
        titleFont.setBold(true);
        titleFont.setFontHeightInPoints((short) 16);
        titleFont.setFontName("宋体");
        titleStyle.setFont(titleFont);

        // 创建第一行的第一个单元格并设置标题文本
        Cell titleCell = titleRow.createCell(0);
        titleCell.setCellValue("沥青路面车辙深度（RD）检测记录");
        titleCell.setCellStyle(titleStyle);

        // 合并第一行的所有列（A到I列）
        newSheet.addMergedRegion(new CellRangeAddress(0, 0, 0, 8));

        // 为其他被合并的单元格也创建相同的样式
        for (int i = 1; i <= 8; i++) {
            Cell cell = titleRow.createCell(i);
            cell.setCellStyle(titleStyle);
        }
    }

    /**
     * 填充RDI数据到模板Sheet
     */
    private void fillRDIDataToTemplate(Sheet sheet, List<RoadCheckRDI> upData, List<RoadCheckRDI> downData,
                                       String roadName, String companyName, String startCode, String endCode) {
        // 创建统一的数据样式
        CellStyle dataStyle = sheet.getWorkbook().createCellStyle();
        dataStyle.setAlignment(HorizontalAlignment.CENTER);
        dataStyle.setVerticalAlignment(VerticalAlignment.CENTER);
        dataStyle.setBorderTop(BorderStyle.THIN);
        dataStyle.setBorderRight(BorderStyle.THIN);
        dataStyle.setBorderBottom(BorderStyle.THIN);
        dataStyle.setBorderLeft(BorderStyle.THIN);
        // 为文本列设置中文字体（宋体）
        Font dataFont = sheet.getWorkbook().createFont();
        dataFont.setFontName("宋体");
        dataFont.setFontHeightInPoints((short) 10);
        dataStyle.setFont(dataFont);

        // 创建数值样式
        DataFormat format = sheet.getWorkbook().createDataFormat();
        CellStyle numberStyle = sheet.getWorkbook().createCellStyle();
        numberStyle.cloneStyleFrom(dataStyle);
        numberStyle.setDataFormat(format.getFormat("0.00"));
        // 为数字列设置Times New Roman字体
        Font numFont = sheet.getWorkbook().createFont();
        numFont.setFontName("Times New Roman");
        numFont.setFontHeightInPoints((short) 10);
        numberStyle.setFont(numFont);

        // 第二行：工程名称和检测单位
        Row row1 = sheet.createRow(1);
        row1.setHeight((short) 400); // 设置行高

        // 合并ABCD列，写入工程名称
        Cell cellA1 = row1.createCell(0);
        cellA1.setCellValue("工程名称：" + (roadName != null ? roadName : ""));
        cellA1.setCellStyle(dataStyle);
        sheet.addMergedRegion(new CellRangeAddress(1, 1, 0, 3)); // 合并A到D列

        // 创建被合并的空单元格
        for (int i = 1; i <= 3; i++) {
            Cell cell = row1.createCell(i);
            cell.setCellStyle(dataStyle);
        }

        // 合并EFGHI列，写入检测单位
        Cell cellE1 = row1.createCell(4);
        cellE1.setCellValue("检测单位：" + companyName);
        cellE1.setCellStyle(dataStyle);
        sheet.addMergedRegion(new CellRangeAddress(1, 1, 4, 8)); // 合并E到I列

        // 创建被合并的空单元格
        for (int i = 5; i <= 8; i++) {
            Cell cell = row1.createCell(i);
            cell.setCellStyle(dataStyle);
        }

        // 第三行：检测项目和检测段落
        Row row2 = sheet.createRow(2);
        row2.setHeight((short) 400); // 设置行高

        // 合并ABCD列，写入检测项目
        Cell cellA2 = row2.createCell(0);
        cellA2.setCellValue("检测项目：路面车辙");
        cellA2.setCellStyle(dataStyle);
        sheet.addMergedRegion(new CellRangeAddress(2, 2, 0, 3)); // 合并A到D列

        // 创建被合并的空单元格
        for (int i = 1; i <= 3; i++) {
            Cell cell = row2.createCell(i);
            cell.setCellStyle(dataStyle);
        }

        // 合并EFGHI列，写入检测段落
        Cell cellE2 = row2.createCell(4);
        String checkSection = (startCode != null ? startCode : "") + "～" + (endCode != null ? endCode : "");
        cellE2.setCellValue("检测段落：" + checkSection);
        cellE2.setCellStyle(dataStyle);
        sheet.addMergedRegion(new CellRangeAddress(2, 2, 4, 8)); // 合并E到I列

        // 创建被合并的空单元格
        for (int i = 5; i <= 8; i++) {
            Cell cell = row2.createCell(i);
            cell.setCellStyle(dataStyle);
        }

        // 第四行：主表头
        Row row3 = sheet.createRow(3);
        row3.setHeight((short) 350);

        // 创建表头样式
        CellStyle headerStyle = sheet.getWorkbook().createCellStyle();
        headerStyle.cloneStyleFrom(dataStyle);
        Font headerFont = sheet.getWorkbook().createFont();
        headerFont.setBold(true);
        headerFont.setFontName("宋体");
        headerStyle.setFont(headerFont);

        // A列：桩号（合并ABC列，并且跨第4、5行）
        Cell cellA3 = row3.createCell(0);
        cellA3.setCellValue("桩号");
        cellA3.setCellStyle(headerStyle);
        sheet.addMergedRegion(new CellRangeAddress(3, 4, 0, 2)); // 合并第4、5行的A到C列

        // 创建被合并的空单元格
        Cell cellB3 = row3.createCell(1);
        cellB3.setCellStyle(headerStyle);
        Cell cellC3 = row3.createCell(2);
        cellC3.setCellStyle(headerStyle);

        // D到F列：上行行车道
        Cell cellD3 = row3.createCell(3);
        cellD3.setCellValue("上行行车道");
        cellD3.setCellStyle(headerStyle);
        sheet.addMergedRegion(new CellRangeAddress(3, 3, 3, 5));
        Cell cellE3 = row3.createCell(4);
        cellE3.setCellStyle(headerStyle);
        Cell cellF3 = row3.createCell(5);
        cellF3.setCellStyle(headerStyle);

        // G到I列：下行行车道
        Cell cellG3 = row3.createCell(6);
        cellG3.setCellValue("下行行车道");
        cellG3.setCellStyle(headerStyle);
        sheet.addMergedRegion(new CellRangeAddress(3, 3, 6, 8));
        Cell cellH3 = row3.createCell(7);
        cellH3.setCellStyle(headerStyle);
        Cell cellI3 = row3.createCell(8);
        cellI3.setCellStyle(headerStyle);

        // 第五行：详细表头
        Row row4 = sheet.createRow(4);
        row4.setHeight((short) 350);

        // 详细表头 - 只需要设置D-I列，其他列已经合并
        // A、B、C列已经被第4行合并，不需要设置
        Cell cellA4 = row4.createCell(0);
        cellA4.setCellStyle(headerStyle);
        Cell cellB4 = row4.createCell(1);
        cellB4.setCellStyle(headerStyle);
        Cell cellC4 = row4.createCell(2);
        cellC4.setCellStyle(headerStyle);

        // D列：左轮迹
        Cell cellD4 = row4.createCell(3);
        cellD4.setCellValue("左轮迹");
        cellD4.setCellStyle(headerStyle);

        // E列：右轮迹
        Cell cellE4 = row4.createCell(4);
        cellE4.setCellValue("右轮迹");
        cellE4.setCellStyle(headerStyle);

        // F列：最大车辙
        Cell cellF4 = row4.createCell(5);
        cellF4.setCellValue("最大车辙");
        cellF4.setCellStyle(headerStyle);

        // G列：左轮迹
        Cell cellG4 = row4.createCell(6);
        cellG4.setCellValue("左轮迹");
        cellG4.setCellStyle(headerStyle);

        // H列：右轮迹
        Cell cellH4 = row4.createCell(7);
        cellH4.setCellValue("右轮迹");
        cellH4.setCellStyle(headerStyle);

        // I列：最大车辙
        Cell cellI4 = row4.createCell(8);
        cellI4.setCellValue("最大车辙");
        cellI4.setCellStyle(headerStyle);

        // 将上行和下行数据按桩号合并
        Map<String, Map<Integer, RoadCheckRDI>> mergedData = new HashMap<>();

        // 处理上行数据
        for (RoadCheckRDI record : upData) {
            String key = record.getStartCode() + "~" + record.getEndCode();
            mergedData.computeIfAbsent(key, k -> new HashMap<>()).put(1, record);
        }

        // 处理下行数据
        for (RoadCheckRDI record : downData) {
            String key = record.getStartCode() + "~" + record.getEndCode();
            mergedData.computeIfAbsent(key, k -> new HashMap<>()).put(2, record);
        }

        // 按桩号排序并填充数据
        List<String> sortedKeys = new ArrayList<>(mergedData.keySet());
        sortedKeys.sort(String::compareTo);

        // 从第六行开始填充数据
        int rowIndex = 5;
        for (String key : sortedKeys) {
            Map<Integer, RoadCheckRDI> directions = mergedData.get(key);
            Row row = sheet.createRow(rowIndex++);
            row.setHeight((short) 350); // 设置数据行高

            // 获取起始和结束桩号
            String[] stakeCodes = key.split("~");
            String startStakeCode = stakeCodes[0];
            String endStakeCode = stakeCodes.length > 1 ? stakeCodes[1] : "";

            // A列：起始桩号
            Cell cellA = row.createCell(0);
            cellA.setCellValue(startStakeCode);
            cellA.setCellStyle(dataStyle);

            // B列：~
            Cell cellB = row.createCell(1);
            cellB.setCellValue("~");
            cellB.setCellStyle(dataStyle);

            // C列：结束桩号
            Cell cellC = row.createCell(2);
            cellC.setCellValue(endStakeCode);
            cellC.setCellStyle(dataStyle);

            // D-F列：上行数据
            RoadCheckRDI upRecord = directions.get(1);
            Cell cellD = row.createCell(3);
            Cell cellE = row.createCell(4);
            Cell cellF = row.createCell(5);

            if (upRecord != null) {
                setDecimalCell(cellD, upRecord.getLeftRd(), numberStyle);
                setDecimalCell(cellE, upRecord.getRightRd(), numberStyle);
                setDecimalCell(cellF, upRecord.getMaxRd(), numberStyle);
            } else {
                cellD.setCellStyle(dataStyle);
                cellE.setCellStyle(dataStyle);
                cellF.setCellStyle(dataStyle);
            }

            // G-I列：下行数据
            RoadCheckRDI downRecord = directions.get(2);
            Cell cellG = row.createCell(6);
            Cell cellH = row.createCell(7);
            Cell cellI = row.createCell(8);

            if (downRecord != null) {
                setDecimalCell(cellG, downRecord.getLeftRd(), numberStyle);
                setDecimalCell(cellH, downRecord.getRightRd(), numberStyle);
                setDecimalCell(cellI, downRecord.getMaxRd(), numberStyle);
            } else {
                cellG.setCellStyle(dataStyle);
                cellH.setCellStyle(dataStyle);
                cellI.setCellStyle(dataStyle);
            }
        }
    }

    /**
     * 设置小数单元格
     */
    private void setDecimalCell(Cell cell, BigDecimal value, CellStyle style) {
        if (value != null) {
            cell.setCellValue(value.doubleValue());
        } else {
            cell.setCellValue(0.0);
        }
        cell.setCellStyle(style);
    }

    /**
     * 计算RDI百米段汇总数据
     */
    private List<Map<String, Object>> calculateRDIHundredSectionSummary(List<RoadCheckRDI> allData) {
        // 按百米段和方向分组
        Map<String, Map<Integer, List<RoadCheckRDI>>> sectionGroups = new HashMap<>();

        for (RoadCheckRDI record : allData) {
            String sectionKey = record.getHundredSection();
            if (sectionKey != null && !sectionKey.trim().isEmpty()) {
                sectionGroups.computeIfAbsent(sectionKey, k -> new HashMap<>())
                        .computeIfAbsent(record.getDirection(), k -> new ArrayList<>())
                        .add(record);
            }
        }

        // 计算每个百米段的汇总数据
        List<Map<String, Object>> result = new ArrayList<>();

        for (Map.Entry<String, Map<Integer, List<RoadCheckRDI>>> sectionEntry : sectionGroups.entrySet()) {
            String section = sectionEntry.getKey();
            Map<Integer, List<RoadCheckRDI>> directionData = sectionEntry.getValue();

            Map<String, Object> summary = new HashMap<>();

            // 获取起始和结束桩号
            String startCode = null;
            String endCode = null;

            for (List<RoadCheckRDI> records : directionData.values()) {
                for (RoadCheckRDI record : records) {
                    if (startCode == null || (record.getStartCode() != null && record.getStartCode().compareTo(startCode) < 0)) {
                        startCode = record.getStartCode();
                    }
                    if (endCode == null || (record.getEndCode() != null && record.getEndCode().compareTo(endCode) > 0)) {
                        endCode = record.getEndCode();
                    }
                }
            }

            summary.put("hundred_section", section);
            summary.put("start_code", startCode);
            summary.put("end_code", endCode);

            // 计算上行数据的最大车辙平均值
            List<RoadCheckRDI> upRecords = directionData.get(1);
            if (upRecords != null && !upRecords.isEmpty()) {
                double avgMaxRd = upRecords.stream()
                        .filter(r -> r.getMaxRd() != null)
                        .mapToDouble(r -> r.getMaxRd().doubleValue())
                        .average()
                        .orElse(0.0);
                summary.put("up_avg_max_rd", avgMaxRd);

                // 计算上行RDI值
                double upRdi = calculateRDI(avgMaxRd);
                summary.put("up_rdi", upRdi);
            }

            // 计算下行数据的最大车辙平均值
            List<RoadCheckRDI> downRecords = directionData.get(2);
            if (downRecords != null && !downRecords.isEmpty()) {
                double avgMaxRd = downRecords.stream()
                        .filter(r -> r.getMaxRd() != null)
                        .mapToDouble(r -> r.getMaxRd().doubleValue())
                        .average()
                        .orElse(0.0);
                summary.put("down_avg_max_rd", avgMaxRd);

                // 计算下行RDI值
                double downRdi = calculateRDI(avgMaxRd);
                summary.put("down_rdi", downRdi);
            }

            result.add(summary);
        }

        // 按百米段排序
        result.sort((a, b) -> {
            String sectionA = (String) a.get("hundred_section");
            String sectionB = (String) b.get("hundred_section");
            return sectionA != null && sectionB != null ? sectionA.compareTo(sectionB) : 0;
        });

        return result;
    }

    /**
     * 计算RDI值：IF(值>40,0,IF(值>10,90-3*值+30,100-值))
     */
    private double calculateRDI(double maxRd) {
        if (maxRd > 40) {
            return 0.0;
        } else if (maxRd > 10) {
            return 90 - 3 * maxRd + 30;
        } else {
            return 100 - maxRd;
        }
    }

    /**
     * 计算RDI公里段汇总数据
     */
    private List<Map<String, Object>> calculateRDIKilometerSectionSummary(List<RoadCheckRDI> allData) {
        // 按公里段和方向分组
        Map<String, Map<Integer, List<RoadCheckRDI>>> sectionGroups = new HashMap<>();

        for (RoadCheckRDI record : allData) {
            String sectionKey = record.getThousandSection();
            if (sectionKey != null && !sectionKey.trim().isEmpty()) {
                sectionGroups.computeIfAbsent(sectionKey, k -> new HashMap<>())
                        .computeIfAbsent(record.getDirection(), k -> new ArrayList<>())
                        .add(record);
            }
        }

        // 计算每个公里段的汇总数据
        List<Map<String, Object>> result = new ArrayList<>();

        for (Map.Entry<String, Map<Integer, List<RoadCheckRDI>>> sectionEntry : sectionGroups.entrySet()) {
            String section = sectionEntry.getKey();
            Map<Integer, List<RoadCheckRDI>> directionData = sectionEntry.getValue();

            Map<String, Object> summary = new HashMap<>();

            // 获取起始和结束桩号
            String startCode = null;
            String endCode = null;

            for (List<RoadCheckRDI> records : directionData.values()) {
                for (RoadCheckRDI record : records) {
                    if (startCode == null || (record.getStartCode() != null && record.getStartCode().compareTo(startCode) < 0)) {
                        startCode = record.getStartCode();
                    }
                    if (endCode == null || (record.getEndCode() != null && record.getEndCode().compareTo(endCode) > 0)) {
                        endCode = record.getEndCode();
                    }
                }
            }

            summary.put("thousand_section", section);
            summary.put("start_code", startCode);
            summary.put("end_code", endCode);

            // 计算实际长度(米)
            int length = calculateStakeCodeDistance(startCode, endCode);
            summary.put("length", length);

            // 计算上行数据的最大车辙平均值
            List<RoadCheckRDI> upRecords = directionData.get(1);
            if (upRecords != null && !upRecords.isEmpty()) {
                double avgMaxRd = upRecords.stream()
                        .filter(r -> r.getMaxRd() != null)
                        .mapToDouble(r -> r.getMaxRd().doubleValue())
                        .average()
                        .orElse(0.0);
                summary.put("up_avg_max_rd", avgMaxRd);

                // 计算上行RDI值
                double upRdi = calculateRDI(avgMaxRd);
                summary.put("up_rdi", upRdi);

                // 计算上行等级
                String upGrade = calculateGrade(upRdi);
                summary.put("up_grade", upGrade);
            }

            // 计算下行数据的最大车辙平均值
            List<RoadCheckRDI> downRecords = directionData.get(2);
            if (downRecords != null && !downRecords.isEmpty()) {
                double avgMaxRd = downRecords.stream()
                        .filter(r -> r.getMaxRd() != null)
                        .mapToDouble(r -> r.getMaxRd().doubleValue())
                        .average()
                        .orElse(0.0);
                summary.put("down_avg_max_rd", avgMaxRd);

                // 计算下行RDI值
                double downRdi = calculateRDI(avgMaxRd);
                summary.put("down_rdi", downRdi);

                // 计算下行等级
                String downGrade = calculateGrade(downRdi);
                summary.put("down_grade", downGrade);
            }

            result.add(summary);
        }

        // 按公里段排序
        result.sort((a, b) -> {
            String sectionA = (String) a.get("thousand_section");
            String sectionB = (String) b.get("thousand_section");
            return sectionA != null && sectionB != null ? sectionA.compareTo(sectionB) : 0;
        });

        return result;
    }

    /**
     * 计算桩号之间的距离(米)
     */
    private int calculateStakeCodeDistance(String startCode, String endCode) {
        if (startCode == null || endCode == null) {
            return 1000; // 默认返回1000米
        }

        try {
            // 简化实现：解析桩号中的数字部分来计算距离
            // 假设桩号格式为 KXX+YYY，其中XX是公里数，YYY是米数

            // 提取起始桩号的公里数和米数
            int[] startParts = parseStakeCode(startCode);
            int[] endParts = parseStakeCode(endCode);

            if (startParts != null && endParts != null) {
                // 将桩号转换为总米数，然后计算差值
                int startTotalMeters = startParts[0] * 1000 + startParts[1];
                int endTotalMeters = endParts[0] * 1000 + endParts[1];
                int distance = Math.abs(endTotalMeters - startTotalMeters);
                return distance > 0 ? distance : 1000;
            }

            return 1000; // 默认返回1000米
        } catch (Exception e) {
            log.warn("计算桩号距离失败: {} -> {}", startCode, endCode, e);
            return 1000; // 默认返回1000米
        }
    }

    /**
     * 解析桩号，返回[公里数, 米数]
     */
    private int[] parseStakeCode(String stakeCode) {
        try {
            if (stakeCode == null || stakeCode.trim().isEmpty()) {
                return null;
            }

            // 移除K前缀，查找+号分隔符
            String code = stakeCode.toUpperCase().replace("K", "");
            String[] parts = code.split("\\+");

            if (parts.length == 2) {
                int km = Integer.parseInt(parts[0]);
                int meter = Integer.parseInt(parts[1]);
                return new int[]{km, meter};
            }

            return null;
        } catch (Exception e) {
            log.warn("解析桩号失败: {}", stakeCode, e);
            return null;
        }
    }

    /**
     * 根据RDI值计算等级
     * 优：大于等于90
     * 良：大于等于80、小于90
     * 中：大于等于70、小于80
     * 次：大于等于60、小于70
     * 差：小于60
     */
    private String calculateGrade(double rdi) {
        if (rdi >= 90) {
            return "优";
        } else if (rdi >= 80) {
            return "良";
        } else if (rdi >= 70) {
            return "中";
        } else if (rdi >= 60) {
            return "次";
        } else {
            return "差";
        }
    }

    /**
     * 填充百米汇总数据到模板Sheet
     */
    private void fillRDIHundredSectionDataToTemplate(Sheet sheet, List<Map<String, Object>> data,
                                                     String roadName, String companyName, String startCode, String endCode) {
        // 创建统一的数据样式
        CellStyle dataStyle = sheet.getWorkbook().createCellStyle();
        dataStyle.setAlignment(HorizontalAlignment.CENTER);
        dataStyle.setVerticalAlignment(VerticalAlignment.CENTER);
        dataStyle.setBorderTop(BorderStyle.THIN);
        dataStyle.setBorderRight(BorderStyle.THIN);
        dataStyle.setBorderBottom(BorderStyle.THIN);
        dataStyle.setBorderLeft(BorderStyle.THIN);
        // 为文本列设置中文字体（宋体）
        Font dataFont = sheet.getWorkbook().createFont();
        dataFont.setFontName("宋体");
        dataFont.setFontHeightInPoints((short) 10);
        dataStyle.setFont(dataFont);

        // 创建数值样式
        DataFormat format = sheet.getWorkbook().createDataFormat();
        CellStyle numberStyle = sheet.getWorkbook().createCellStyle();
        numberStyle.cloneStyleFrom(dataStyle);
        numberStyle.setDataFormat(format.getFormat("0.00"));
        // 为数字列设置Times New Roman字体
        Font numFont = sheet.getWorkbook().createFont();
        numFont.setFontName("Times New Roman");
        numFont.setFontHeightInPoints((short) 10);
        numberStyle.setFont(numFont);

        // 第二行：工程名称和检测单位
        Row row1 = sheet.createRow(1);
        row1.setHeight((short) 400);

        // 合并ABCD列，写入工程名称
        Cell cellA1 = row1.createCell(0);
        cellA1.setCellValue("工程名称：" + (roadName != null ? roadName : ""));
        cellA1.setCellStyle(dataStyle);
        sheet.addMergedRegion(new CellRangeAddress(1, 1, 0, 3));

        for (int i = 1; i <= 3; i++) {
            Cell cell = row1.createCell(i);
            cell.setCellStyle(dataStyle);
        }

        // 合并EFGHI列，写入检测单位
        Cell cellE1 = row1.createCell(4);
        cellE1.setCellValue("检测单位：" + companyName);
        cellE1.setCellStyle(dataStyle);
        sheet.addMergedRegion(new CellRangeAddress(1, 1, 4, 6));

        for (int i = 5; i <= 6; i++) {
            Cell cell = row1.createCell(i);
            cell.setCellStyle(dataStyle);
        }

        // 第三行：检测项目和检测段落
        Row row2 = sheet.createRow(2);
        row2.setHeight((short) 400);

        // 合并ABCD列，写入检测项目
        Cell cellA2 = row2.createCell(0);
        cellA2.setCellValue("检测项目：路面车辙");
        cellA2.setCellStyle(dataStyle);
        sheet.addMergedRegion(new CellRangeAddress(2, 2, 0, 3));

        for (int i = 1; i <= 3; i++) {
            Cell cell = row2.createCell(i);
            cell.setCellStyle(dataStyle);
        }

        // 合并EFGHI列，写入检测段落
        Cell cellE2 = row2.createCell(4);
        String checkSection = (startCode != null ? startCode : "") + "～" + (endCode != null ? endCode : "");
        cellE2.setCellValue("检测段落：" + checkSection);
        cellE2.setCellStyle(dataStyle);
        sheet.addMergedRegion(new CellRangeAddress(2, 2, 4, 6));

        for (int i = 5; i <= 6; i++) {
            Cell cell = row2.createCell(i);
            cell.setCellStyle(dataStyle);
        }

        // 第四行：主表头
        Row row3 = sheet.createRow(3);
        row3.setHeight((short) 350);

        // 创建表头样式
        CellStyle headerStyle = sheet.getWorkbook().createCellStyle();
        headerStyle.cloneStyleFrom(dataStyle);
        Font headerFont = sheet.getWorkbook().createFont();
        headerFont.setBold(true);
        headerFont.setFontName("宋体");
        headerStyle.setFont(headerFont);

        // A列：桩号（合并ABC列，并且跨第4、5行）
        Cell cellA3 = row3.createCell(0);
        cellA3.setCellValue("桩号");
        cellA3.setCellStyle(headerStyle);
        sheet.addMergedRegion(new CellRangeAddress(3, 4, 0, 2));

        Cell cellB3 = row3.createCell(1);
        cellB3.setCellStyle(headerStyle);
        Cell cellC3 = row3.createCell(2);
        cellC3.setCellStyle(headerStyle);

        // D到E列：上行方向行车道
        Cell cellD3 = row3.createCell(3);
        cellD3.setCellValue("上行方向行车道");
        cellD3.setCellStyle(headerStyle);
        sheet.addMergedRegion(new CellRangeAddress(3, 3, 3, 4));
        Cell cellE3 = row3.createCell(4);
        cellE3.setCellStyle(headerStyle);

        // F到G列：下行方向行车道
        Cell cellF3 = row3.createCell(5);
        cellF3.setCellValue("下行方向行车道");
        cellF3.setCellStyle(headerStyle);
        sheet.addMergedRegion(new CellRangeAddress(3, 3, 5, 6));
        Cell cellG3 = row3.createCell(6);
        cellG3.setCellStyle(headerStyle);

        // 第五行：详细表头
        Row row4 = sheet.createRow(4);
        row4.setHeight((short) 350);

        // ABC列已经被第4行合并
        Cell cellA4 = row4.createCell(0);
        cellA4.setCellStyle(headerStyle);
        Cell cellB4 = row4.createCell(1);
        cellB4.setCellStyle(headerStyle);
        Cell cellC4 = row4.createCell(2);
        cellC4.setCellStyle(headerStyle);

        // D列：RD
        Cell cellD4 = row4.createCell(3);
        cellD4.setCellValue("RD");
        cellD4.setCellStyle(headerStyle);

        // E列：RDI
        Cell cellE4 = row4.createCell(4);
        cellE4.setCellValue("RDI");
        cellE4.setCellStyle(headerStyle);

        // F列：RD
        Cell cellF4 = row4.createCell(5);
        cellF4.setCellValue("RD");
        cellF4.setCellStyle(headerStyle);

        // G列：RDI
        Cell cellG4 = row4.createCell(6);
        cellG4.setCellValue("RDI");
        cellG4.setCellStyle(headerStyle);

        // 从第六行开始填充数据
        int rowIndex = 5;
        for (Map<String, Object> record : data) {
            Row row = sheet.createRow(rowIndex++);
            row.setHeight((short) 350);

            String recordStartCode = (String) record.get("start_code");
            String recordEndCode = (String) record.get("end_code");

            // A列：起始桩号
            Cell cellA = row.createCell(0);
            cellA.setCellValue(recordStartCode != null ? recordStartCode : "");
            cellA.setCellStyle(dataStyle);

            // B列：~
            Cell cellB = row.createCell(1);
            cellB.setCellValue("~");
            cellB.setCellStyle(dataStyle);

            // C列：结束桩号
            Cell cellC = row.createCell(2);
            cellC.setCellValue(recordEndCode != null ? recordEndCode : "");
            cellC.setCellStyle(dataStyle);

            // D列：上行RD平均值
            Cell cellD = row.createCell(3);
            Double upAvgMaxRd = (Double) record.get("up_avg_max_rd");
            if (upAvgMaxRd != null) {
                cellD.setCellValue(upAvgMaxRd);
            } else {
                cellD.setCellValue(0.0);
            }
            cellD.setCellStyle(numberStyle);

            // E列：上行RDI值
            Cell cellE = row.createCell(4);
            Double upRdi = (Double) record.get("up_rdi");
            if (upRdi != null) {
                cellE.setCellValue(upRdi);
            } else {
                cellE.setCellValue(0.0);
            }
            cellE.setCellStyle(numberStyle);

            // F列：下行RD平均值
            Cell cellF = row.createCell(5);
            Double downAvgMaxRd = (Double) record.get("down_avg_max_rd");
            if (downAvgMaxRd != null) {
                cellF.setCellValue(downAvgMaxRd);
            } else {
                cellF.setCellValue(0.0);
            }
            cellF.setCellStyle(numberStyle);

            // G列：下行RDI值
            Cell cellG = row.createCell(6);
            Double downRdi = (Double) record.get("down_rdi");
            if (downRdi != null) {
                cellG.setCellValue(downRdi);
            } else {
                cellG.setCellValue(0.0);
            }
            cellG.setCellStyle(numberStyle);
        }
    }

    /**
     * 填充公里汇总数据到模板Sheet
     */
    private void fillRDIKilometerSectionDataToTemplate(Sheet sheet, List<Map<String, Object>> data, String roadName) {
        // 设置第1、2行合并的标题
        Cell titleCell = sheet.getRow(0).getCell(0);
        titleCell.setCellValue(roadName + "公路路面车辙及车辙深度指数汇总表");

        // 创建统一的数据样式
        CellStyle dataStyle = sheet.getWorkbook().createCellStyle();
        dataStyle.setAlignment(HorizontalAlignment.CENTER);
        dataStyle.setVerticalAlignment(VerticalAlignment.CENTER);
        dataStyle.setBorderTop(BorderStyle.THIN);
        dataStyle.setBorderRight(BorderStyle.THIN);
        dataStyle.setBorderBottom(BorderStyle.THIN);
        dataStyle.setBorderLeft(BorderStyle.THIN);
        // 为文本列设置中文字体（宋体）
        Font dataFont = sheet.getWorkbook().createFont();
        dataFont.setFontName("宋体");
        dataFont.setFontHeightInPoints((short) 10);
        dataStyle.setFont(dataFont);

        // 创建数值样式
        DataFormat format = sheet.getWorkbook().createDataFormat();
        CellStyle numberStyle = sheet.getWorkbook().createCellStyle();
        numberStyle.cloneStyleFrom(dataStyle);
        numberStyle.setDataFormat(format.getFormat("0.00"));
        // 为数字列设置Times New Roman字体
        Font numFont = sheet.getWorkbook().createFont();
        numFont.setFontName("Times New Roman");
        numFont.setFontHeightInPoints((short) 10);
        numberStyle.setFont(numFont);

        // 创建表头样式
        CellStyle headerStyle = sheet.getWorkbook().createCellStyle();
        headerStyle.cloneStyleFrom(dataStyle);
        Font headerFont = sheet.getWorkbook().createFont();
        headerFont.setBold(true);
        headerFont.setFontName("宋体");
        headerStyle.setFont(headerFont);

        // 第三行：主表头
        Row row2 = sheet.createRow(2);
        row2.setHeight((short) 350);

        // A列：桩号（合并ABC列，并且跨第3、4行）
        Cell cellA2 = row2.createCell(0);
        cellA2.setCellValue("桩号");
        cellA2.setCellStyle(headerStyle);
        sheet.addMergedRegion(new CellRangeAddress(2, 3, 0, 2));

        Cell cellB2 = row2.createCell(1);
        cellB2.setCellStyle(headerStyle);
        Cell cellC2 = row2.createCell(2);
        cellC2.setCellStyle(headerStyle);

        // D列：里程
        Cell cellD2 = row2.createCell(3);
        cellD2.setCellValue("里程");
        cellD2.setCellStyle(headerStyle);
        sheet.addMergedRegion(new CellRangeAddress(2, 3, 3, 3));

        // E到G列：上行方向
        Cell cellE2 = row2.createCell(4);
        cellE2.setCellValue("上行方向");
        cellE2.setCellStyle(headerStyle);
        sheet.addMergedRegion(new CellRangeAddress(2, 2, 4, 6));
        Cell cellF2 = row2.createCell(5);
        cellF2.setCellStyle(headerStyle);
        Cell cellG2 = row2.createCell(6);
        cellG2.setCellStyle(headerStyle);

        // H到J列：下行方向
        Cell cellH2 = row2.createCell(7);
        cellH2.setCellValue("下行方向");
        cellH2.setCellStyle(headerStyle);
        sheet.addMergedRegion(new CellRangeAddress(2, 2, 7, 9));
        Cell cellI2 = row2.createCell(8);
        cellI2.setCellStyle(headerStyle);
        Cell cellJ2 = row2.createCell(9);
        cellJ2.setCellStyle(headerStyle);

        // 第四行：详细表头
        Row row3 = sheet.createRow(3);
        row3.setHeight((short) 350);

        // ABC列已经被第3行合并
        Cell cellA3 = row3.createCell(0);
        cellA3.setCellStyle(headerStyle);
        Cell cellB3 = row3.createCell(1);
        cellB3.setCellStyle(headerStyle);
        Cell cellC3 = row3.createCell(2);
        cellC3.setCellStyle(headerStyle);

        // D列已经被第3行合并
        Cell cellD3 = row3.createCell(3);
        cellD3.setCellStyle(headerStyle);

        // E列：车辙(RD)
        Cell cellE3 = row3.createCell(4);
        cellE3.setCellValue("车辙(RD)");
        cellE3.setCellStyle(headerStyle);

        // F列：车辙深度指数
        Cell cellF3 = row3.createCell(5);
        cellF3.setCellValue("车辙深度指数(RDI)");
        cellF3.setCellStyle(headerStyle);

        // G列：等级
        Cell cellG3 = row3.createCell(6);
        cellG3.setCellValue("等级");
        cellG3.setCellStyle(headerStyle);

        // H列：车辙(RD)
        Cell cellH3 = row3.createCell(7);
        cellH3.setCellValue("车辙(RD)");
        cellH3.setCellStyle(headerStyle);

        // I列：车辙深度指数
        Cell cellI3 = row3.createCell(8);
        cellI3.setCellValue("车辙深度指数(RDI)");
        cellI3.setCellStyle(headerStyle);

        // J列：等级
        Cell cellJ3 = row3.createCell(9);
        cellJ3.setCellValue("等级");
        cellJ3.setCellStyle(headerStyle);

        // 从第五行开始填充数据
        int rowIndex = 4;
        int totalLength = 0;
        double totalUpRd = 0.0;
        double totalDownRd = 0.0;
        int upRecordCount = 0;
        int downRecordCount = 0;

        // 统计各等级的里程
        Map<String, Integer> upGradeLength = new HashMap<>();
        Map<String, Integer> downGradeLength = new HashMap<>();
        upGradeLength.put("优", 0);
        upGradeLength.put("良", 0);
        upGradeLength.put("中", 0);
        upGradeLength.put("次", 0);
        upGradeLength.put("差", 0);
        downGradeLength.put("优", 0);
        downGradeLength.put("良", 0);
        downGradeLength.put("中", 0);
        downGradeLength.put("次", 0);
        downGradeLength.put("差", 0);

        for (Map<String, Object> record : data) {
            Row row = sheet.createRow(rowIndex++);
            row.setHeight((short) 350);

            String recordStartCode = (String) record.get("start_code");
            String recordEndCode = (String) record.get("end_code");

            // A列：起始桩号
            Cell cellA = row.createCell(0);
            cellA.setCellValue(recordStartCode != null ? recordStartCode : "");
            cellA.setCellStyle(dataStyle);

            // B列：~
            Cell cellB = row.createCell(1);
            cellB.setCellValue("~");
            cellB.setCellStyle(dataStyle);

            // C列：结束桩号
            Cell cellC = row.createCell(2);
            cellC.setCellValue(recordEndCode != null ? recordEndCode : "");
            cellC.setCellStyle(dataStyle);

            // D列：里程(米)
            Cell cellD = row.createCell(3);
            Integer length = (Integer) record.get("length");
            int currentLength = length != null ? length : 0;
            cellD.setCellValue(currentLength);
            cellD.setCellStyle(dataStyle);
            totalLength += currentLength;

            // E列：上行RD平均值
            Cell cellE = row.createCell(4);
            Double upAvgMaxRd = (Double) record.get("up_avg_max_rd");
            if (upAvgMaxRd != null) {
                cellE.setCellValue(upAvgMaxRd);
                totalUpRd += upAvgMaxRd;
                upRecordCount++;
            } else {
                cellE.setCellValue(0.0);
            }
            cellE.setCellStyle(numberStyle);

            // F列：上行RDI值
            Cell cellF = row.createCell(5);
            Double upRdi = (Double) record.get("up_rdi");
            if (upRdi != null) {
                cellF.setCellValue(upRdi);
            } else {
                cellF.setCellValue(0.0);
            }
            cellF.setCellStyle(numberStyle);

            // G列：上行等级
            Cell cellG = row.createCell(6);
            String upGrade = (String) record.get("up_grade");
            cellG.setCellValue(upGrade != null ? upGrade : "");
            cellG.setCellStyle(dataStyle);

            // 统计上行等级里程
            if (upGrade != null && upGradeLength.containsKey(upGrade)) {
                upGradeLength.put(upGrade, upGradeLength.get(upGrade) + currentLength);
            }

            // H列：下行RD平均值
            Cell cellH = row.createCell(7);
            Double downAvgMaxRd = (Double) record.get("down_avg_max_rd");
            if (downAvgMaxRd != null) {
                cellH.setCellValue(downAvgMaxRd);
                totalDownRd += downAvgMaxRd;
                downRecordCount++;
            } else {
                cellH.setCellValue(0.0);
            }
            cellH.setCellStyle(numberStyle);

            // I列：下行RDI值
            Cell cellI = row.createCell(8);
            Double downRdi = (Double) record.get("down_rdi");
            if (downRdi != null) {
                cellI.setCellValue(downRdi);
            } else {
                cellI.setCellValue(0.0);
            }
            cellI.setCellStyle(numberStyle);

            // J列：下行等级
            Cell cellJ = row.createCell(9);
            String downGrade = (String) record.get("down_grade");
            cellJ.setCellValue(downGrade != null ? downGrade : "");
            cellJ.setCellStyle(dataStyle);

            // 统计下行等级里程
            if (downGrade != null && downGradeLength.containsKey(downGrade)) {
                downGradeLength.put(downGrade, downGradeLength.get(downGrade) + currentLength);
            }
        }

        // 添加合计行
        Row totalRow = sheet.createRow(rowIndex++);
        totalRow.setHeight((short) 350);

        // 合并A-C列，显示"合计"
        Cell totalCellA = totalRow.createCell(0);
        totalCellA.setCellValue("合计");
        totalCellA.setCellStyle(headerStyle);
        sheet.addMergedRegion(new CellRangeAddress(rowIndex - 1, rowIndex - 1, 0, 2));
        Cell totalCellB = totalRow.createCell(1);
        totalCellB.setCellStyle(headerStyle);
        Cell totalCellC = totalRow.createCell(2);
        totalCellC.setCellStyle(headerStyle);

        // D列：总里程
        Cell totalCellD = totalRow.createCell(3);
        totalCellD.setCellValue(totalLength);
        totalCellD.setCellStyle(headerStyle);

        // E列：上行RD平均值
        Cell totalCellE = totalRow.createCell(4);
        double avgUpRd = upRecordCount > 0 ? totalUpRd / upRecordCount : 0.0;
        totalCellE.setCellValue(avgUpRd);
        totalCellE.setCellStyle(numberStyle);

        // F列：上行总体RDI值
        Cell totalCellF = totalRow.createCell(5);
        double totalUpRdi = calculateRDI(avgUpRd);
        totalCellF.setCellValue(totalUpRdi);
        totalCellF.setCellStyle(numberStyle);

        // G列：上行总体等级
        Cell totalCellG = totalRow.createCell(6);
        totalCellG.setCellValue(calculateGrade(totalUpRdi));
        totalCellG.setCellStyle(headerStyle);

        // H列：下行RD平均值
        Cell totalCellH = totalRow.createCell(7);
        double avgDownRd = downRecordCount > 0 ? totalDownRd / downRecordCount : 0.0;
        totalCellH.setCellValue(avgDownRd);
        totalCellH.setCellStyle(numberStyle);

        // I列：下行总体RDI值
        Cell totalCellI = totalRow.createCell(8);
        double totalDownRdi = calculateRDI(avgDownRd);
        totalCellI.setCellValue(totalDownRdi);
        totalCellI.setCellStyle(numberStyle);

        // J列：下行总体等级
        Cell totalCellJ = totalRow.createCell(9);
        totalCellJ.setCellValue(calculateGrade(totalDownRdi));
        totalCellJ.setCellStyle(headerStyle);

        // 添加等级统计行
        String[] grades = {"优", "良", "中", "次", "差"};
        for (String grade : grades) {
            Row gradeRow = sheet.createRow(rowIndex++);
            gradeRow.setHeight((short) 350);

            // 合并A-C列，显示等级(%)
            Cell gradeCellA = gradeRow.createCell(0);
            gradeCellA.setCellValue(grade + " (%)");
            gradeCellA.setCellStyle(dataStyle);
            sheet.addMergedRegion(new CellRangeAddress(rowIndex - 1, rowIndex - 1, 0, 2));
            Cell gradeCellB = gradeRow.createCell(1);
            gradeCellB.setCellStyle(dataStyle);
            Cell gradeCellC = gradeRow.createCell(2);
            gradeCellC.setCellStyle(dataStyle);

            // D列：空着
            Cell gradeCellD = gradeRow.createCell(3);
            gradeCellD.setCellStyle(dataStyle);

            // E列：上行该等级里程
            Cell gradeCellE = gradeRow.createCell(4);
            int upLength = upGradeLength.get(grade);
            gradeCellE.setCellValue(upLength);
            gradeCellE.setCellStyle(dataStyle);

            // 合并F-G列：上行百分比
            Cell gradeCellF = gradeRow.createCell(5);
            double upPercentage = totalLength > 0 ? (double) upLength / totalLength * 100 : 0.0;
            gradeCellF.setCellValue(String.format("%.2f%%", upPercentage));
            gradeCellF.setCellStyle(dataStyle);
            sheet.addMergedRegion(new CellRangeAddress(rowIndex - 1, rowIndex - 1, 5, 6));
            Cell gradeCellG = gradeRow.createCell(6);
            gradeCellG.setCellStyle(dataStyle);

            // H列：下行该等级里程
            Cell gradeCellH = gradeRow.createCell(7);
            int downLength = downGradeLength.get(grade);
            gradeCellH.setCellValue(downLength);
            gradeCellH.setCellStyle(dataStyle);

            // 合并I-J列：下行百分比
            Cell gradeCellI = gradeRow.createCell(8);
            double downPercentage = totalLength > 0 ? (double) downLength / totalLength * 100 : 0.0;
            gradeCellI.setCellValue(String.format("%.2f%%", downPercentage));
            gradeCellI.setCellStyle(dataStyle);
            sheet.addMergedRegion(new CellRangeAddress(rowIndex - 1, rowIndex - 1, 8, 9));
            Cell gradeCellJ = gradeRow.createCell(9);
            gradeCellJ.setCellStyle(dataStyle);
        }
    }

    @Override
    public void exportWordRDIByDirection(HttpServletResponse response, Long roadId, Long teamId, String dateTime, String monthDate, String titleName, String checkName, String reviewName) {
        try {
            // 1. 查询数据
            RoadCheckRDI query = new RoadCheckRDI();
            query.setRoadId(roadId);

            query.setDirection(1);
            List<RoadCheckRDI> upData = roadCheckRDMapper.selectRoadCheckRDList(query);
            query.setDirection(2);
            List<RoadCheckRDI> downData = roadCheckRDMapper.selectRoadCheckRDList(query);

            List<RoadCheckRDI> allData = new ArrayList<>();
            allData.addAll(upData);
            allData.addAll(downData);

            if (CollectionUtils.isEmpty(allData)) {
                response.setContentType("application/json;charset=UTF-8");
                response.getWriter().write("{\"code\":500,\"msg\":\"没有找到对应的RDI数据\"}");
                return;
            }

            Road road = roadMapper.selectRoadById(roadId);
            if (road == null) {
                response.setContentType("application/json;charset=UTF-8");
                response.getWriter().write("{\"code\":500,\"msg\":\"路线信息不存在\"}");
                return;
            }

            // 2. 加载模板
            // 解决POI安全限制：设置ZIP文件的最小压缩比率，避免Zip bomb检测误报
            org.apache.poi.openxml4j.util.ZipSecureFile.setMinInflateRatio(0.001);

            ClassPathResource resource = new ClassPathResource("static/word/rdi-template.docx");
            InputStream templateInputStream = resource.getInputStream();
            XWPFDocument document = new XWPFDocument(templateInputStream);

            // 3. 填充检测人员信息到第二个表格（如果存在）
            if (document.getTables().size() >= 2) {
                Long actualTeamId = teamId;
                if (actualTeamId != null) {
                    checkTeamService.fillCheckTeamUserTable(document.getTables().get(1), actualTeamId);
                    log.info("成功填充RDI检测人员信息表格，使用分组ID: {}", actualTeamId);
                }
            } else {
                log.warn("未找到RDI检测人员信息表格（第二个表格）");
            }

            // 4. 替换占位符
            String roadName = road.getRoadName() != null ? road.getRoadName() : "";
            String companyName = road.getCompanyName() != null ? road.getCompanyName() : "湖北交投智能检测股份有限公司";
            String startCode = road.getStartCode();
            String endCode = road.getEndCode();
            String year = String.valueOf(road.getYear());
            String date = StringUtils.isNotEmpty(dateTime) ? dateTime : DateUtils.getDate();
            String monthDateStr = StringUtils.isNotEmpty(monthDate) ? monthDate : DateUtils.parseDateToStr("yyyy年MM月", new Date());

            // 按内容分段设置字体：中文宋体、字母/数字Times New Roman
            WordDocumentUtils.replaceTextInDocument(document, "${roadName}", road.getRoadName());
            WordDocumentUtils.replaceTextInDocument(document, "${startCode}", road.getStartCode());
            WordDocumentUtils.replaceTextInDocument(document, "${endCode}", road.getEndCode());
            WordDocumentUtils.replaceTextInDocumentWithFontStyle(document, "${roadNames}", road.getRoadName(),"黑体",24);
            WordDocumentUtils.replaceTextInDocumentWithFontStyle(document, "${startCodes}", road.getStartCode(),"黑体",24);
            WordDocumentUtils.replaceTextInDocumentWithFontStyle(document, "${endCodes}", road.getEndCode(),"黑体",24);
            WordDocumentUtils.replaceTextInDocumentWithFontStyle(document, "${year}", String.valueOf(road.getYear()),"黑体",24);
            WordDocumentUtils.replaceTextInDocument(document, "${roadNameTitle}", roadName);
            WordDocumentUtils.replaceTextInDocument(document, "${companyName}", companyName);
            WordDocumentUtils.replaceTextInDocument(document, "${date}", date);
            WordDocumentUtils.replaceTextInDocument(document, "${monthDate}", monthDateStr);
            WordDocumentUtils.replaceTextInDocument(document, "${projectName}", road.getProjectName());
            WordDocumentUtils.replaceTextInDocument(document, "${reportNo}", road.getReportNo());
            WordDocumentUtils.replaceTextInDocument(document, "${number}", "03");
            // 替换日期相关占位符
            if (dateTime != null && !dateTime.isEmpty()) {
                replaceRDITextInDocument(document, "${dateTime}", dateTime);
            }

            // 根据dateTime参数生成dateTimeStr并替换
            if (dateTime != null && !dateTime.isEmpty()) {
                String dateTimeStr = dateTime.replace("年", ".").replace("月", ".").replace("日", "");
                replaceRDITextInDocument(document, "${dateTimeStr}", dateTimeStr);
            }

            // 5. 计算公里汇总数据
            List<Map<String, Object>> kilometerSectionData = calculateRDIKilometerSectionSummary(allData);

            // 计算文档页数并替换页数占位符
            int[] pageCalculations = calculateRDIDocumentPagesWithDetails(kilometerSectionData, allData);
            int totalPages = pageCalculations[0];
            int pageOne = pageCalculations[1];
            int pageTwo = pageCalculations[2];

            WordDocumentUtils.replaceTextInDocument(document, "${pages}", String.valueOf(totalPages));
            WordDocumentUtils.replaceTextInDocument(document, "${pageOne}", String.valueOf(pageOne));
            WordDocumentUtils.replaceTextInDocument(document, "${pageTwo}", String.valueOf(pageTwo));
            log.info("RDI文档共 {} 页, pageOne: {}, pageTwo: {}", totalPages, pageOne, pageTwo);

            // 使用公共工具类替换标准占位符（包括报告编号）
            WordDocumentUtils.replaceStandardPlaceholders(document, "RDI", titleName, checkName, reviewName, road);

            // 6. 填充表格
            if (document.getTables().size() >= 3) {
                XWPFTable table = document.getTables().get(2); // 第三个表格
                fillRDIKilometerDataToWordTable(table, kilometerSectionData);
            } else {
                log.error("RDI Word模板文件 rdi-template.docx 中没有找到第三个表格。");
                response.setContentType("application/json;charset=UTF-8");
                response.getWriter().write("{\"code\":500,\"msg\":\"Word模板格式不正确，缺少数据表。\"}");
                return;
            }

            // 填充最后一个表格（十米数据）
            if (document.getTables().size() >= 4) {
                XWPFTable detailTable = document.getTables().get(document.getTables().size() - 1);
                fillRDIDetailDataToWordTable(detailTable, upData, downData);
            } else {
                log.warn("Word模板中未找到用于填充十米详细数据的最后一个表格。将跳过此步骤。");
            }

            // 7. 输出文件
            String fileName = URLEncoder.encode(
                    "沥青路面车辙深度（RD）检测记录_" + road.getRoadCode() + "_" +
                            DateUtils.dateTimeNow() + ".docx", "UTF-8"
            );
            response.setContentType("application/vnd.openxmlformats-officedocument.wordprocessingml.document");
            response.setHeader("Content-Disposition", "attachment; filename=\"" + fileName + "\"");

            document.write(response.getOutputStream());
            document.close();
            templateInputStream.close();

        } catch (Exception e) {
            log.error("导出RDI Word文档失败", e);
            try {
                response.setContentType("application/json;charset=UTF-8");
                response.getWriter().write("{\"code\":500,\"msg\":\"导出Word失败：" + e.getMessage() + "\"}");
            } catch (IOException ex) {
                log.error("写入错误响应失败", ex);
            }
        }
    }

    /**
     * 填充RDI十米详细数据到Word表格
     */
    private void fillRDIDetailDataToWordTable(XWPFTable table, List<RoadCheckRDI> upData, List<RoadCheckRDI> downData) {
        // 合并表头的前三列
        mergeCellsHorizontally(table, 0, 0, 2);
        mergeCellsHorizontally(table, 1, 0, 2);

        // 从第三行开始清除旧数据
        while (table.getRows().size() > 2) {
            table.removeRow(2);
        }

        // 合并上行和下行数据
        Map<String, Map<Integer, RoadCheckRDI>> mergedData = new HashMap<>();
        for (RoadCheckRDI record : upData) {
            String key = record.getStartCode() + "~" + record.getEndCode();
            mergedData.computeIfAbsent(key, k -> new HashMap<>()).put(1, record);
        }
        for (RoadCheckRDI record : downData) {
            String key = record.getStartCode() + "~" + record.getEndCode();
            mergedData.computeIfAbsent(key, k -> new HashMap<>()).put(2, record);
        }

        // 按桩号排序并填充数据
        List<String> sortedKeys = new ArrayList<>(mergedData.keySet());
        sortedKeys.sort(String::compareTo);

        for (String key : sortedKeys) {
            Map<Integer, RoadCheckRDI> directions = mergedData.get(key);
            XWPFTableRow row = table.createRow();

            String[] stakeCodes = key.split("~");
            String startStakeCode = stakeCodes[0];
            String endStakeCode = stakeCodes.length > 1 ? stakeCodes[1] : "";

            safeSetRDICellText(row, 0, startStakeCode);
            WordDocumentUtils.setCellTextWithVerticalCenter(row, 1, "~");
            safeSetRDICellText(row, 2, endStakeCode);

            RoadCheckRDI upRecord = directions.get(1);
            if (upRecord != null) {
                safeSetRDIDecimalCellText(row, 3, upRecord.getLeftRd());
                safeSetRDIDecimalCellText(row, 4, upRecord.getRightRd());
                safeSetRDIDecimalCellText(row, 5, upRecord.getMaxRd());
            } else {
                safeSetRDICellText(row, 3, "0.00");
                safeSetRDICellText(row, 4, "0.00");
                safeSetRDICellText(row, 5, "0.00");
            }

            RoadCheckRDI downRecord = directions.get(2);
            if (downRecord != null) {
                safeSetRDIDecimalCellText(row, 6, downRecord.getLeftRd());
                safeSetRDIDecimalCellText(row, 7, downRecord.getRightRd());
                safeSetRDIDecimalCellText(row, 8, downRecord.getMaxRd());
            } else {
                safeSetRDICellText(row, 6, "0.00");
                safeSetRDICellText(row, 7, "0.00");
                safeSetRDICellText(row, 8, "0.00");
            }
        }
    }

    /**
     * 安全地设置单元格的小数值
     */
    private void safeSetRDIDecimalCellText(XWPFTableRow row, int cellIndex, BigDecimal value) {
        String text = (value != null) ? String.format("%.2f", value.doubleValue()) : "0.00";
        safeSetRDICellText(row, cellIndex, text);
    }

    /**
     * 填充RDI公里汇总数据到Word表格
     */
    private void fillRDIKilometerDataToWordTable(XWPFTable table, List<Map<String, Object>> data) {
        // 合并表头第一行的前三列以匹配 "桩号"
        mergeCellsHorizontally(table, 0, 0, 2);
        // 合并表头第二行的前三列
        mergeCellsHorizontally(table, 1, 0, 2);

        // 清理模板中可能存在的旧数据行（保留前2行作为表头）
        while (table.getRows().size() > 2) {
            table.removeRow(2);
        }

        int totalLength = 0;
        double totalUpRd = 0.0;
        double totalDownRd = 0.0;
        int upRecordCount = 0;
        int downRecordCount = 0;

        Map<String, Integer> upGradeLength = new HashMap<>();
        Map<String, Integer> downGradeLength = new HashMap<>();
        String[] grades = {"优", "良", "中", "次", "差"};
        for (String grade : grades) {
            upGradeLength.put(grade, 0);
            downGradeLength.put(grade, 0);
        }

        // 填充数据行
        for (Map<String, Object> record : data) {
            XWPFTableRow dataRow = table.createRow();

            String recordStartCode = (String) record.get("start_code");
            String recordEndCode = (String) record.get("end_code");

            safeSetRDICellText(dataRow, 0, recordStartCode != null ? recordStartCode : "");
            com.tunnel.common.utils.WordDocumentUtils.setCellTextWithVerticalCenter(dataRow, 1, "~");
            safeSetRDICellText(dataRow, 2, recordEndCode != null ? recordEndCode : "");

            Integer length = (Integer) record.get("length");
            int currentLength = length != null ? length : 0;
            safeSetRDICellText(dataRow, 3, String.valueOf(currentLength));
            totalLength += currentLength;

            Double upAvgMaxRd = (Double) record.get("up_avg_max_rd");
            if (upAvgMaxRd != null) {
                safeSetRDICellText(dataRow, 4, String.format("%.2f", upAvgMaxRd));
                totalUpRd += upAvgMaxRd;
                upRecordCount++;
            } else {
                safeSetRDICellText(dataRow, 4, "0.00");
            }

            Double upRdi = (Double) record.get("up_rdi");
            safeSetRDICellText(dataRow, 5, upRdi != null ? String.format("%.2f", upRdi) : "0.00");

            String upGrade = (String) record.get("up_grade");
            safeSetRDICellText(dataRow, 6, upGrade != null ? upGrade : "");
            if (upGrade != null && upGradeLength.containsKey(upGrade)) {
                upGradeLength.put(upGrade, upGradeLength.get(upGrade) + currentLength);
            }

            Double downAvgMaxRd = (Double) record.get("down_avg_max_rd");
            if (downAvgMaxRd != null) {
                safeSetRDICellText(dataRow, 7, String.format("%.2f", downAvgMaxRd));
                totalDownRd += downAvgMaxRd;
                downRecordCount++;
            } else {
                safeSetRDICellText(dataRow, 7, "0.00");
            }

            Double downRdi = (Double) record.get("down_rdi");
            safeSetRDICellText(dataRow, 8, downRdi != null ? String.format("%.2f", downRdi) : "0.00");

            String downGrade = (String) record.get("down_grade");
            safeSetRDICellText(dataRow, 9, downGrade != null ? downGrade : "");
            if (downGrade != null && downGradeLength.containsKey(downGrade)) {
                downGradeLength.put(downGrade, downGradeLength.get(downGrade) + currentLength);
            }
        }

        // 添加合计行
        XWPFTableRow totalRow = table.createRow();
        int totalRowIndex = table.getRows().size() - 1;
        safeSetRDICellText(totalRow, 0, "合计");
        mergeCellsHorizontally(table, totalRowIndex, 0, 2);
        safeSetRDICellText(totalRow, 1, "");
        safeSetRDICellText(totalRow, 2, "");

        safeSetRDICellText(totalRow, 3, String.valueOf(totalLength));

        double avgUpRd = upRecordCount > 0 ? totalUpRd / upRecordCount : 0.0;
        safeSetRDICellText(totalRow, 4, String.format("%.2f", avgUpRd));
        double totalUpRdi = calculateRDI(avgUpRd);
        safeSetRDICellText(totalRow, 5, String.format("%.2f", totalUpRdi));
        safeSetRDICellText(totalRow, 6, calculateGrade(totalUpRdi));

        double avgDownRd = downRecordCount > 0 ? totalDownRd / downRecordCount : 0.0;
        safeSetRDICellText(totalRow, 7, String.format("%.2f", avgDownRd));
        double totalDownRdi = calculateRDI(avgDownRd);
        safeSetRDICellText(totalRow, 8, String.format("%.2f", totalDownRdi));
        safeSetRDICellText(totalRow, 9, calculateGrade(totalDownRdi));

        // 添加等级统计行
        for (String grade : grades) {
            addRDIGradeRowToWord(table, grade,
                    upGradeLength.get(grade), downGradeLength.get(grade), totalLength);
        }
    }

    /**
     * 向Word表格添加一行等级统计数据
     */
    private void addRDIGradeRowToWord(XWPFTable table, String gradeName, int upLength, int downLength, int totalLength) {
        XWPFTableRow gradeRow = table.createRow();
        int rowIndex = table.getRows().size() - 1;

        // A-C (0-2): 等级名称
        safeSetRDICellText(gradeRow, 0, gradeName + " (%)");
        mergeCellsHorizontally(table, rowIndex, 0, 2);
        safeSetRDICellText(gradeRow, 1, "");
        safeSetRDICellText(gradeRow, 2, "");

        // D (3): 空白
        safeSetRDICellText(gradeRow, 3, "");

        // E (4): 上行里程
        safeSetRDICellText(gradeRow, 4, String.valueOf(upLength));

        // F-G (5-6): 上行百分比
        double upPercentage = totalLength > 0 ? (double) upLength / totalLength * 100 : 0.0;
        safeSetRDICellText(gradeRow, 5, String.format("%.2f%%", upPercentage));
        mergeCellsHorizontally(table, rowIndex, 5, 6);
        safeSetRDICellText(gradeRow, 6, "");

        // H (7): 下行里程
        safeSetRDICellText(gradeRow, 7, String.valueOf(downLength));

        // I-J (8-9): 下行百分比
        double downPercentage = totalLength > 0 ? (double) downLength / totalLength * 100 : 0.0;
        safeSetRDICellText(gradeRow, 8, String.format("%.2f%%", downPercentage));
        mergeCellsHorizontally(table, rowIndex, 8, 9);
        safeSetRDICellText(gradeRow, 9, "");
    }


    /**
     * 在段落中替换文本，完全保持原有格式
     * 这个方法逐个run进行处理，确保格式不被破坏
     */
    private void replaceTextInParagraphPreservingFormat(XWPFParagraph paragraph, String placeholder, String replacement) {
        try {
            List<XWPFRun> runs = paragraph.getRuns();
            if (runs == null || runs.isEmpty()) {
                return;
            }

            // 构建完整的段落文本，同时记录每个字符对应的run索引
            StringBuilder fullText = new StringBuilder();
            List<Integer> charToRunMap = new ArrayList<>();

            for (int runIndex = 0; runIndex < runs.size(); runIndex++) {
                XWPFRun run = runs.get(runIndex);
                String runText = run.getText(0);
                if (runText != null) {
                    fullText.append(runText);
                    for (int i = 0; i < runText.length(); i++) {
                        charToRunMap.add(runIndex);
                    }
                }
            }

            String originalText = fullText.toString();
            if (!originalText.contains(placeholder)) {
                return;
            }

            // 找到占位符的位置
            int placeholderStart = originalText.indexOf(placeholder);
            int placeholderEnd = placeholderStart + placeholder.length();

            // 确定占位符所在的run范围
            if (placeholderStart >= 0 && placeholderStart < charToRunMap.size()) {
                int targetRunIndex = charToRunMap.get(placeholderStart);
                XWPFRun targetRun = runs.get(targetRunIndex);

                // 保存目标run的所有格式信息
                String fontFamily = targetRun.getFontFamily();
                Integer fontSize = targetRun.getFontSize();
                Boolean isBold = targetRun.isBold();
                Boolean isItalic = targetRun.isItalic();
                String color = targetRun.getColor();
                Boolean isUnderlined = targetRun.getUnderline() != null;

                // 替换文本
                String newText = originalText.replace(placeholder, replacement);

                // 清除所有run的文本
                for (XWPFRun run : runs) {
                    run.setText("", 0);
                }

                // 将新文本设置到目标run中，保持其格式
                targetRun.setText(newText, 0);

                // 确保格式完全保持
                if (fontFamily != null) {
                    targetRun.setFontFamily(fontFamily);
                }
                if (fontSize != null && fontSize > 0) {
                    targetRun.setFontSize(fontSize);
                }
                if (isBold != null) {
                    targetRun.setBold(isBold);
                }
                if (isItalic != null) {
                    targetRun.setItalic(isItalic);
                }
                if (color != null && !color.isEmpty()) {
                    targetRun.setColor(color);
                }
                if (isUnderlined != null && isUnderlined) {
                    targetRun.setUnderline(targetRun.getUnderline());
                }

                log.debug("页眉标题替换成功：{} -> {}，保持原格式（字体: {}, 大小: {}）",
                        placeholder, replacement, fontFamily, fontSize);
            }
        } catch (Exception e) {
            log.warn("保持格式的文本替换失败: {}", e.getMessage());
            // 如果精确替换失败，回退到简单替换
            try {
                String text = paragraph.getText();
                if (text != null && text.contains(placeholder)) {
                    // 简单的整体替换作为备用方案
                    for (XWPFRun run : paragraph.getRuns()) {
                        String runText = run.getText(0);
                        if (runText != null && runText.contains(placeholder)) {
                            run.setText(runText.replace(placeholder, replacement), 0);
                            break;
                        }
                    }
                }
            } catch (Exception fallbackException) {
                log.warn("备用文本替换也失败: {}", fallbackException.getMessage());
            }
        }
    }

    /**
     * 替换整个文档中的占位符
     */
    private void replaceRDITextInDocument(XWPFDocument document, String placeholder, String value) {
        try {
            // 替换段落中的文本
            for (XWPFParagraph p : document.getParagraphs()) {
                // 对所有占位符都使用格式保持的替换方法
                replaceTextInParagraphPreservingFormat(p, placeholder, value != null ? value : "");
            }

            // 替换表格中的文本
            for (XWPFTable table : document.getTables()) {
                for (XWPFTableRow row : table.getRows()) {
                    for (XWPFTableCell cell : row.getTableCells()) {
                        for (XWPFParagraph p : cell.getParagraphs()) {
                            // 对所有占位符都使用格式保持的替换方法
                            replaceTextInParagraphPreservingFormat(p, placeholder, value != null ? value : "");
                        }
                    }
                }
            }

            // 替换页眉中的文本
            for (XWPFHeader header : document.getHeaderList()) {
                for (XWPFParagraph paragraph : header.getParagraphs()) {
                    // 对所有占位符都使用格式保持的替换方法
                    replaceTextInParagraphPreservingFormat(paragraph, placeholder, value != null ? value : "");
                }
                for (XWPFTable table : header.getTables()) {
                    for (XWPFTableRow row : table.getRows()) {
                        for (XWPFTableCell cell : row.getTableCells()) {
                            for (XWPFParagraph paragraph : cell.getParagraphs()) {
                                // 对所有占位符都使用格式保持的替换方法
                                replaceTextInParagraphPreservingFormat(paragraph, placeholder, value != null ? value : "");
                            }
                        }
                    }
                }
            }

            // 替换页脚中的文本
            for (XWPFFooter footer : document.getFooterList()) {
                for (XWPFParagraph paragraph : footer.getParagraphs()) {
                    // 对所有占位符都使用格式保持的替换方法
                    replaceTextInParagraphPreservingFormat(paragraph, placeholder, value != null ? value : "");
                }
                for (XWPFTable table : footer.getTables()) {
                    for (XWPFTableRow row : table.getRows()) {
                        for (XWPFTableCell cell : row.getTableCells()) {
                            for (XWPFParagraph paragraph : cell.getParagraphs()) {
                                // 对所有占位符都使用格式保持的替换方法
                                replaceTextInParagraphPreservingFormat(paragraph, placeholder, value != null ? value : "");
                            }
                        }
                    }
                }
            }
        } catch (Exception e) {
            log.warn("替换RDI文档占位符失败 [{}]: {}", placeholder, e.getMessage());
        }
    }

    /**
     * 替换段落中的占位符
     */
    private void replaceRDIParagraphText(XWPFParagraph p, String placeholder, String value) {
        String text = p.getText();
        if (text != null && text.contains(placeholder)) {
            // 使用 Apache Commons Text 的 StrSubstitutor 来处理复杂情况
            // 这里简化处理，直接替换
            String replacedText = text.replace(placeholder, value);

            // 清除段落中所有的 run
            for (int i = p.getRuns().size() - 1; i >= 0; i--) {
                p.removeRun(i);
            }

            // 创建新的 run 并设置文本
            XWPFRun run = p.createRun();
            run.setText(replacedText);
        }
    }

    /**
     * 安全地设置单元格文本，并统一格式
     */
    private void safeSetRDICellText(XWPFTableRow row, int cellIndex, String text) {
        XWPFTableCell cell = row.getCell(cellIndex);
        if (cell == null) {
            cell = row.addNewTableCell();
        }

        // 垂直居中
        CTTcPr tcPr = cell.getCTTc().isSetTcPr() ? cell.getCTTc().getTcPr() : cell.getCTTc().addNewTcPr();
        tcPr.addNewVAlign().setVal(STVerticalJc.CENTER);

        // 设置实线边框
        CTTcBorders borders = tcPr.isSetTcBorders() ? tcPr.getTcBorders() : tcPr.addNewTcBorders();

        CTBorder topBorder = borders.isSetTop() ? borders.getTop() : borders.addNewTop();
        topBorder.setVal(STBorder.SINGLE);
        topBorder.setSz(BigInteger.valueOf(4)); // 0.25 pt

        CTBorder bottomBorder = borders.isSetBottom() ? borders.getBottom() : borders.addNewBottom();
        bottomBorder.setVal(STBorder.SINGLE);
        bottomBorder.setSz(BigInteger.valueOf(4));

        CTBorder leftBorder = borders.isSetLeft() ? borders.getLeft() : borders.addNewLeft();
        leftBorder.setVal(STBorder.SINGLE);
        leftBorder.setSz(BigInteger.valueOf(4));

        CTBorder rightBorder = borders.isSetRight() ? borders.getRight() : borders.addNewRight();
        rightBorder.setVal(STBorder.SINGLE);
        rightBorder.setSz(BigInteger.valueOf(4));

        // 清除现有段落
        for (int i = cell.getParagraphs().size() - 1; i >= 0; i--) {
            cell.removeParagraph(i);
        }

        // 添加新段落，按内容分段设置字体（数字/字母TNR，中文宋体）
        XWPFParagraph p = cell.addParagraph();
        p.setAlignment(ParagraphAlignment.CENTER);
        appendRDITextWithFonts(p, text != null ? text : "", 9, null, null, null);
    }

    /**
     * 将文本按"字母/数字"和"中文/其他"分段写入段落，分别设置字体。
     * - 字母/数字使用 Times New Roman
     * - 其他文字使用 宋体
     * 可选择性地继承字号/加粗/斜体/颜色
     */
    private void appendRDITextWithFonts(XWPFParagraph paragraph, String text, Integer inheritFontSize,
                                        Boolean inheritBold, Boolean inheritItalic, String inheritColor) {
        if (text == null) {
            return;
        }
        StringBuilder segment = new StringBuilder();
        Boolean currentLatin = null;
        for (int i = 0; i < text.length(); i++) {
            char c = text.charAt(i);
            boolean isLatin = isLatinDigitRDI(c);
            if (currentLatin == null) {
                currentLatin = isLatin;
            }
            if (isLatin != currentLatin) {
                createRDIRunWithFont(paragraph, segment.toString(), currentLatin,
                        inheritFontSize, inheritBold, inheritItalic, inheritColor);
                segment.setLength(0);
                currentLatin = isLatin;
            }
            segment.append(c);
        }
        createRDIRunWithFont(paragraph, segment.toString(), currentLatin != null ? currentLatin : false,
                inheritFontSize, inheritBold, inheritItalic, inheritColor);
    }

    private boolean isLatinDigitRDI(char c) {
        if (Character.isDigit(c)) return true;
        return (c >= 'A' && c <= 'Z') || (c >= 'a' && c <= 'z');
    }

    private void createRDIRunWithFont(XWPFParagraph paragraph, String content, boolean latin,
                                      Integer inheritFontSize, Boolean inheritBold, Boolean inheritItalic, String inheritColor) {
        if (content == null || content.isEmpty()) {
            return;
        }
        XWPFRun run = paragraph.createRun();
        run.setText(content);
        run.setFontFamily(latin ? "Times New Roman" : "宋体");
        if (inheritFontSize != null && inheritFontSize > 0) {
            run.setFontSize(inheritFontSize);
        } else {
            run.setFontSize(10);
        }
        if (inheritBold != null) run.setBold(inheritBold);
        if (inheritItalic != null) run.setItalic(inheritItalic);
        if (inheritColor != null && !inheritColor.isEmpty()) run.setColor(inheritColor);
    }

    /**
     * 水平合并单元格
     */
    private void mergeCellsHorizontally(XWPFTable table, int row, int fromCol, int toCol) {
        if (row >= table.getRows().size()) return;
        XWPFTableRow tableRow = table.getRow(row);
        if (fromCol > toCol) return;
        if (tableRow.getTableCells().size() <= toCol) return;

        for (int col = fromCol; col <= toCol; col++) {
            XWPFTableCell cell = tableRow.getCell(col);
            if (cell == null) continue;
            CTTcPr tcPr = cell.getCTTc().isSetTcPr() ? cell.getCTTc().getTcPr() : cell.getCTTc().addNewTcPr();
            if (col == fromCol) {
                // The first cell in a merged group is marked as restart
                tcPr.addNewHMerge().setVal(STMerge.RESTART);
            } else {
                // Subsequent cells in a merged group are marked as continue
                tcPr.addNewHMerge().setVal(STMerge.CONTINUE);
            }
        }
    }

    @Override
    public int deleteScRoadCheckRdByRoadId(Long roadId) {
        return roadCheckRDMapper.deleteRoadCheckRDByRoadId(roadId);
    }

    /**
     * 计算RDI Word文档的页数（返回详细信息）
     * @return int数组 [总页数, pageOne, pageTwo]
     */
    private int[] calculateRDIDocumentPagesWithDetails(List<Map<String, Object>> summaryData, List<RoadCheckRDI> detailData) {
        try {
            // 基础页数
            int basePages = 0;

            // 计算汇总表格页数
            int summaryRows = summaryData != null ? summaryData.size() : 0;
            int summaryPages = calculateTablePages(summaryRows + 6, 38, 41); // +6是固定的合计行和等级行

            // 计算详细数据表格页数（合并上下行数据，一行数据对应一个桩号段）
            int dataRows = detailData != null ? detailData.size() : 0;
            // RDI的详细数据表是合并显示上下行的，所以实际行数是桩号段数
            Set<String> uniqueStakeSections = new HashSet<>();
            if (detailData != null) {
                for (RoadCheckRDI record : detailData) {
                    String key = record.getStartCode() + "~" + record.getEndCode();
                    uniqueStakeSections.add(key);
                }
                dataRows = uniqueStakeSections.size();
            }
            int dataPages = calculateTablePages(dataRows, 40, 42);

            // 计算 pageOne = 汇总表格的总页码 + 6
            int pageOne = summaryPages + basePages;

            // 计算 pageTwo = pageOne + 1
            int pageTwo = pageOne + 1;

            // 总页数计算
            int totalPages = basePages + summaryPages + dataPages;

            log.info("RDI页数计算详情：基础页数={}, 汇总行数={}, 汇总页数={}, 详细数据行数={}, 数据页数={}, 总页数={}, pageOne={}, pageTwo={}",
                    basePages, summaryRows, summaryPages, dataRows, dataPages, totalPages, pageOne, pageTwo);

            return new int[]{totalPages, pageOne, pageTwo};

        } catch (Exception e) {
            log.warn("计算RDI文档页数失败，使用默认值: {}", e.getMessage());
            return new int[]{10, 13, 14}; // 默认返回页数
        }
    }

    /**
     * 计算RDI Word文档的页数（保持向后兼容性）
     * 考虑第一页的特殊行数限制：
     * - 汇总表格第一页只有38行数据，后续页41行
     * - 上/下行表格第一页只有40行数据，后续页42行
     *
     * @param summaryData 汇总数据
     * @param detailData 详细数据
     * @return 文档页数
     */
    private int calculateRDIDocumentPages(List<Map<String, Object>> summaryData, List<RoadCheckRDI> detailData) {
        int[] calculations = calculateRDIDocumentPagesWithDetails(summaryData, detailData);
        return calculations[0]; // 返回总页数
    }

    /**
     * 计算表格页数，考虑第一页和后续页的不同行数限制
     *
     * @param totalRows 总行数
     * @param firstPageRows 第一页可容纳的行数
     * @param otherPageRows 后续页可容纳的行数
     * @return 所需页数
     */
    private int calculateTablePages(int totalRows, int firstPageRows, int otherPageRows) {
        if (totalRows <= 0) {
            return 0;
        }

        if (totalRows <= firstPageRows) {
            // 第一页就能容纳所有数据
            return 1;
        } else {
            // 需要多页
            int remainingRows = totalRows - firstPageRows; // 除第一页外的剩余行数
            int additionalPages = (int) Math.ceil((double) remainingRows / otherPageRows); // 额外需要的页数
            return 1 + additionalPages; // 第一页 + 额外页数
        }
    }
}  